/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PerformanceMonitor, measureAsync } from '../telemetry/performanceMonitor.js';

describe('PerformanceMonitor', () => {
  let monitor: PerformanceMonitor;

  beforeEach(() => {
    monitor = new PerformanceMonitor({
      enabled: true,
      sampleRate: 1.0,
      maxSpans: 100,
      maxMetrics: 500,
      enableMemoryTracking: false, // Disable for testing
      enableCpuTracking: false,
    });
  });

  afterEach(() => {
    monitor.clear();
  });

  describe('span management', () => {
    it('should create and end spans correctly', () => {
      const spanId = monitor.startSpan('test-operation');
      expect(spanId).toBeTruthy();

      const span = monitor.endSpan(spanId);
      expect(span).toBeDefined();
      expect(span?.name).toBe('test-operation');
      expect(span?.duration).toBeGreaterThan(0);
    });

    it('should handle nested spans', () => {
      const parentSpanId = monitor.startSpan('parent-operation');
      const childSpanId = monitor.startSpan('child-operation', parentSpanId);

      const childSpan = monitor.endSpan(childSpanId);
      const parentSpan = monitor.endSpan(parentSpanId);

      expect(childSpan?.parentId).toBe(parentSpanId);
      expect(parentSpan?.parentId).toBeUndefined();
    });

    it('should add tags to spans', () => {
      const spanId = monitor.startSpan('test-operation', undefined, { userId: '123', action: 'read' });
      const span = monitor.endSpan(spanId, { result: 'success' });

      expect(span?.tags).toEqual({
        userId: '123',
        action: 'read',
        result: 'success',
      });
    });

    it('should return null for invalid span IDs', () => {
      const span = monitor.endSpan('invalid-span-id');
      expect(span).toBeNull();
    });
  });

  describe('metrics recording', () => {
    it('should record custom metrics', () => {
      monitor.recordMetric({
        name: 'test.metric',
        value: 42,
        unit: 'count',
        timestamp: Date.now(),
        tags: { type: 'test' },
      });

      const metrics = monitor.getMetrics();
      expect(metrics).toHaveLength(1);
      expect(metrics[0].name).toBe('test.metric');
      expect(metrics[0].value).toBe(42);
    });

    it('should record file operation metrics', () => {
      monitor.recordFileOperation('read', '/test/file.txt', 150, true);

      const metrics = monitor.getMetrics();
      const durationMetric = metrics.find(m => m.name === 'file.read.duration');
      const countMetric = metrics.find(m => m.name === 'file.read.count');

      expect(durationMetric).toBeDefined();
      expect(durationMetric?.value).toBe(150);
      expect(durationMetric?.tags?.success).toBe('true');

      expect(countMetric).toBeDefined();
      expect(countMetric?.value).toBe(1);
    });

    it('should record API call metrics', () => {
      monitor.recordApiCall('/api/users', 'GET', 250, 200);

      const metrics = monitor.getMetrics();
      const durationMetric = metrics.find(m => m.name === 'api.call.duration');
      const countMetric = metrics.find(m => m.name === 'api.call.count');

      expect(durationMetric).toBeDefined();
      expect(durationMetric?.value).toBe(250);
      expect(durationMetric?.tags?.endpoint).toBe('/api/users');
      expect(durationMetric?.tags?.method).toBe('GET');
      expect(durationMetric?.tags?.status_code).toBe('200');

      expect(countMetric).toBeDefined();
      expect(countMetric?.value).toBe(1);
    });

    it('should record tool execution metrics', () => {
      monitor.recordToolExecution('file-reader', 100, true);

      const metrics = monitor.getMetrics();
      const durationMetric = metrics.find(m => m.name === 'tool.execution.duration');
      const countMetric = metrics.find(m => m.name === 'tool.execution.count');

      expect(durationMetric).toBeDefined();
      expect(durationMetric?.value).toBe(100);
      expect(durationMetric?.tags?.tool_name).toBe('file-reader');
      expect(durationMetric?.tags?.success).toBe('true');

      expect(countMetric).toBeDefined();
      expect(countMetric?.value).toBe(1);
    });
  });

  describe('performance summary', () => {
    it('should generate performance summary', () => {
      // Create some spans
      const span1Id = monitor.startSpan('operation1');
      const span2Id = monitor.startSpan('operation2');
      
      // Add some delay to ensure measurable duration
      setTimeout(() => {
        monitor.endSpan(span1Id);
        monitor.endSpan(span2Id);
      }, 10);

      // Record some metrics
      monitor.recordMetric({
        name: 'test.metric1',
        value: 10,
        unit: 'count',
        timestamp: Date.now(),
      });
      monitor.recordMetric({
        name: 'test.metric1',
        value: 20,
        unit: 'count',
        timestamp: Date.now(),
      });
      monitor.recordMetric({
        name: 'test.metric2',
        value: 5,
        unit: 'ms',
        timestamp: Date.now(),
      });

      const summary = monitor.getPerformanceSummary();

      expect(summary.totalSpans).toBe(2);
      expect(summary.totalMetrics).toBe(3);
      expect(summary.metricsSummary['test.metric1']).toBeDefined();
      expect(summary.metricsSummary['test.metric1'].count).toBe(2);
      expect(summary.metricsSummary['test.metric1'].average).toBe(15);
      expect(summary.metricsSummary['test.metric1'].min).toBe(10);
      expect(summary.metricsSummary['test.metric1'].max).toBe(20);
    });
  });

  describe('configuration', () => {
    it('should respect sampling rate', () => {
      const sampledMonitor = new PerformanceMonitor({
        enabled: true,
        sampleRate: 0.0, // No sampling
      });

      const spanId = sampledMonitor.startSpan('test-operation');
      expect(spanId).toBe(''); // Should return empty string when not sampled
    });

    it('should respect enabled flag', () => {
      const disabledMonitor = new PerformanceMonitor({
        enabled: false,
      });

      disabledMonitor.recordMetric({
        name: 'test.metric',
        value: 42,
        unit: 'count',
        timestamp: Date.now(),
      });

      const metrics = disabledMonitor.getMetrics();
      expect(metrics).toHaveLength(0);
    });

    it('should cleanup old spans when max limit is reached', () => {
      const limitedMonitor = new PerformanceMonitor({
        enabled: true,
        maxSpans: 2,
      });

      // Create more spans than the limit
      const span1Id = limitedMonitor.startSpan('operation1');
      const span2Id = limitedMonitor.startSpan('operation2');
      const span3Id = limitedMonitor.startSpan('operation3');

      limitedMonitor.endSpan(span1Id);
      limitedMonitor.endSpan(span2Id);
      limitedMonitor.endSpan(span3Id);

      const spans = limitedMonitor.getSpans();
      expect(spans.length).toBeLessThanOrEqual(2);
    });
  });

  describe('metrics filtering', () => {
    it('should filter metrics by pattern', () => {
      monitor.recordMetric({
        name: 'api.call.duration',
        value: 100,
        unit: 'ms',
        timestamp: Date.now(),
      });
      monitor.recordMetric({
        name: 'file.read.duration',
        value: 50,
        unit: 'ms',
        timestamp: Date.now(),
      });
      monitor.recordMetric({
        name: 'api.call.count',
        value: 1,
        unit: 'count',
        timestamp: Date.now(),
      });

      const apiMetrics = monitor.getMetricsByPattern('api\\.');
      expect(apiMetrics).toHaveLength(2);
      expect(apiMetrics.every(m => m.name.startsWith('api.'))).toBe(true);

      const durationMetrics = monitor.getMetricsByPattern('\\.duration$');
      expect(durationMetrics).toHaveLength(2);
      expect(durationMetrics.every(m => m.name.endsWith('.duration'))).toBe(true);
    });
  });

  describe('data export', () => {
    it('should export performance data', () => {
      const spanId = monitor.startSpan('test-operation');
      monitor.endSpan(spanId);
      
      monitor.recordMetric({
        name: 'test.metric',
        value: 42,
        unit: 'count',
        timestamp: Date.now(),
      });

      const exportedData = monitor.exportData();

      expect(exportedData.spans).toHaveLength(1);
      expect(exportedData.metrics).toHaveLength(2); // 1 custom + 1 from span
      expect(exportedData.summary).toBeDefined();
      expect(exportedData.summary.totalSpans).toBe(1);
      expect(exportedData.summary.totalMetrics).toBe(2);
    });
  });
});

describe('measureAsync utility', () => {
  it('should measure async operation duration', async () => {
    const result = await measureAsync('test-operation', async () => {
      await new Promise(resolve => setTimeout(resolve, 50));
      return 'success';
    });

    expect(result).toBe('success');
    
    // Check that metrics were recorded
    const monitor = require('../telemetry/performanceMonitor.js').getPerformanceMonitor();
    const metrics = monitor.getMetrics();
    const durationMetric = metrics.find((m: any) => m.name === 'operation.test-operation.duration');
    
    expect(durationMetric).toBeDefined();
    expect(durationMetric.value).toBeGreaterThan(40); // Should be around 50ms
  });

  it('should handle errors in async operations', async () => {
    const testError = new Error('Test error');
    
    await expect(measureAsync('failing-operation', async () => {
      throw testError;
    })).rejects.toThrow('Test error');

    // Check that error metrics were recorded
    const monitor = require('../telemetry/performanceMonitor.js').getPerformanceMonitor();
    const metrics = monitor.getMetrics();
    const durationMetric = metrics.find((m: any) => m.name === 'operation.failing-operation.duration');
    
    expect(durationMetric).toBeDefined();
    expect(durationMetric.tags?.success).toBe('false');
  });
});
