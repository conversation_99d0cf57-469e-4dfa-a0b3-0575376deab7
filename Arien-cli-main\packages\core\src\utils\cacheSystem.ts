/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { createHash } from 'crypto';

/**
 * Cache entry with metadata
 */
export interface CacheEntry<T = any> {
  key: string;
  value: T;
  timestamp: number;
  ttl?: number; // Time to live in milliseconds
  accessCount: number;
  lastAccessed: number;
  size?: number; // Size in bytes
  tags?: string[];
}

/**
 * Cache statistics
 */
export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitCount: number;
  missCount: number;
  hitRate: number;
  evictionCount: number;
  oldestEntry?: number;
  newestEntry?: number;
}

/**
 * Cache configuration
 */
export interface CacheConfig {
  maxSize: number; // Maximum number of entries
  maxMemorySize: number; // Maximum memory size in bytes
  defaultTtl: number; // Default TTL in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
  persistToDisk: boolean;
  diskCachePath?: string;
  enableCompression: boolean;
  enableEncryption: boolean;
  encryptionKey?: string;
}

/**
 * Cache eviction strategies
 */
export enum EvictionStrategy {
  LRU = 'lru', // Least Recently Used
  LFU = 'lfu', // Least Frequently Used
  FIFO = 'fifo', // First In, First Out
  TTL = 'ttl', // Time To Live based
}

/**
 * Advanced caching system with multiple strategies and persistence
 */
export class AdvancedCache<T = any> {
  private cache: Map<string, CacheEntry<T>> = new Map();
  private config: CacheConfig;
  private stats: CacheStats;
  private cleanupTimer?: NodeJS.Timeout;
  private evictionStrategy: EvictionStrategy;

  constructor(
    config: Partial<CacheConfig> = {},
    evictionStrategy: EvictionStrategy = EvictionStrategy.LRU
  ) {
    this.config = {
      maxSize: 1000,
      maxMemorySize: 100 * 1024 * 1024, // 100MB
      defaultTtl: 60 * 60 * 1000, // 1 hour
      cleanupInterval: 5 * 60 * 1000, // 5 minutes
      persistToDisk: false,
      enableCompression: false,
      enableEncryption: false,
      ...config,
    };

    this.evictionStrategy = evictionStrategy;
    this.stats = {
      totalEntries: 0,
      totalSize: 0,
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      evictionCount: 0,
    };

    this.startCleanupTimer();
    
    if (this.config.persistToDisk) {
      this.loadFromDisk();
    }
  }

  /**
   * Gets a value from the cache
   */
  async get(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.missCount++;
      this.updateHitRate();
      return null;
    }

    // Check if entry has expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.stats.missCount++;
      this.updateHitRate();
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    this.stats.hitCount++;
    this.updateHitRate();

    return entry.value;
  }

  /**
   * Sets a value in the cache
   */
  async set(key: string, value: T, ttl?: number, tags?: string[]): Promise<void> {
    const now = Date.now();
    const size = this.calculateSize(value);
    
    const entry: CacheEntry<T> = {
      key,
      value,
      timestamp: now,
      ttl: ttl || this.config.defaultTtl,
      accessCount: 0,
      lastAccessed: now,
      size,
      tags,
    };

    // Check if we need to evict entries
    await this.ensureCapacity(size);

    // Add or update the entry
    const existingEntry = this.cache.get(key);
    if (existingEntry) {
      this.stats.totalSize -= existingEntry.size || 0;
    } else {
      this.stats.totalEntries++;
    }

    this.cache.set(key, entry);
    this.stats.totalSize += size;

    // Persist to disk if enabled
    if (this.config.persistToDisk) {
      await this.persistEntry(entry);
    }
  }

  /**
   * Deletes a value from the cache
   */
  async delete(key: string): Promise<boolean> {
    const entry = this.cache.get(key);
    if (!entry) {
      return false;
    }

    this.cache.delete(key);
    this.stats.totalEntries--;
    this.stats.totalSize -= entry.size || 0;

    // Remove from disk if enabled
    if (this.config.persistToDisk) {
      await this.removeFromDisk(key);
    }

    return true;
  }

  /**
   * Checks if a key exists in the cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    return entry !== undefined && !this.isExpired(entry);
  }

  /**
   * Clears all entries from the cache
   */
  async clear(): Promise<void> {
    this.cache.clear();
    this.stats.totalEntries = 0;
    this.stats.totalSize = 0;

    if (this.config.persistToDisk && this.config.diskCachePath) {
      try {
        await fs.rmdir(this.config.diskCachePath, { recursive: true });
      } catch (error) {
        console.error('Error clearing disk cache:', error);
      }
    }
  }

  /**
   * Gets cache statistics
   */
  getStats(): CacheStats {
    const entries = Array.from(this.cache.values());
    const timestamps = entries.map(e => e.timestamp);
    
    return {
      ...this.stats,
      oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : undefined,
      newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : undefined,
    };
  }

  /**
   * Gets all keys in the cache
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Gets all values in the cache
   */
  values(): T[] {
    return Array.from(this.cache.values()).map(entry => entry.value);
  }

  /**
   * Gets entries by tag
   */
  getByTag(tag: string): Array<{ key: string; value: T }> {
    const results: Array<{ key: string; value: T }> = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags && entry.tags.includes(tag) && !this.isExpired(entry)) {
        results.push({ key, value: entry.value });
      }
    }
    
    return results;
  }

  /**
   * Removes entries by tag
   */
  async deleteByTag(tag: string): Promise<number> {
    let deletedCount = 0;
    const keysToDelete: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags && entry.tags.includes(tag)) {
        keysToDelete.push(key);
      }
    }
    
    for (const key of keysToDelete) {
      if (await this.delete(key)) {
        deletedCount++;
      }
    }
    
    return deletedCount;
  }

  /**
   * Gets or sets a value with a factory function
   */
  async getOrSet(
    key: string,
    factory: () => Promise<T> | T,
    ttl?: number,
    tags?: string[]
  ): Promise<T> {
    const cached = await this.get(key);
    if (cached !== null) {
      return cached;
    }

    const value = await factory();
    await this.set(key, value, ttl, tags);
    return value;
  }

  /**
   * Refreshes an entry's TTL
   */
  async refresh(key: string, ttl?: number): Promise<boolean> {
    const entry = this.cache.get(key);
    if (!entry) {
      return false;
    }

    entry.ttl = ttl || this.config.defaultTtl;
    entry.timestamp = Date.now();
    
    if (this.config.persistToDisk) {
      await this.persistEntry(entry);
    }
    
    return true;
  }

  /**
   * Exports cache data
   */
  export(): Array<{ key: string; value: T; metadata: Omit<CacheEntry<T>, 'value'> }> {
    const exports: Array<{ key: string; value: T; metadata: Omit<CacheEntry<T>, 'value'> }> = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (!this.isExpired(entry)) {
        const { value, ...metadata } = entry;
        exports.push({ key, value, metadata });
      }
    }
    
    return exports;
  }

  /**
   * Imports cache data
   */
  async import(data: Array<{ key: string; value: T; metadata: Omit<CacheEntry<T>, 'value'> }>): Promise<void> {
    for (const item of data) {
      const entry: CacheEntry<T> = {
        ...item.metadata,
        value: item.value,
      };
      
      if (!this.isExpired(entry)) {
        this.cache.set(item.key, entry);
        this.stats.totalEntries++;
        this.stats.totalSize += entry.size || 0;
      }
    }
  }

  /**
   * Ensures cache capacity by evicting entries if necessary
   */
  private async ensureCapacity(newEntrySize: number): Promise<void> {
    // Check size limits
    while (
      this.cache.size >= this.config.maxSize ||
      this.stats.totalSize + newEntrySize > this.config.maxMemorySize
    ) {
      await this.evictEntry();
    }
  }

  /**
   * Evicts an entry based on the configured strategy
   */
  private async evictEntry(): Promise<void> {
    if (this.cache.size === 0) return;

    let keyToEvict: string | null = null;

    switch (this.evictionStrategy) {
      case EvictionStrategy.LRU:
        keyToEvict = this.findLRUKey();
        break;
      case EvictionStrategy.LFU:
        keyToEvict = this.findLFUKey();
        break;
      case EvictionStrategy.FIFO:
        keyToEvict = this.findFIFOKey();
        break;
      case EvictionStrategy.TTL:
        keyToEvict = this.findExpiredKey();
        break;
    }

    if (keyToEvict) {
      await this.delete(keyToEvict);
      this.stats.evictionCount++;
    }
  }

  /**
   * Finds the least recently used key
   */
  private findLRUKey(): string | null {
    let oldestKey: string | null = null;
    let oldestTime = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  /**
   * Finds the least frequently used key
   */
  private findLFUKey(): string | null {
    let leastUsedKey: string | null = null;
    let leastCount = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.accessCount < leastCount) {
        leastCount = entry.accessCount;
        leastUsedKey = key;
      }
    }

    return leastUsedKey;
  }

  /**
   * Finds the first in, first out key
   */
  private findFIFOKey(): string | null {
    let oldestKey: string | null = null;
    let oldestTime = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  /**
   * Finds an expired key
   */
  private findExpiredKey(): string | null {
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        return key;
      }
    }
    return null;
  }

  /**
   * Checks if an entry has expired
   */
  private isExpired(entry: CacheEntry<T>): boolean {
    if (!entry.ttl) return false;
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * Calculates the size of a value
   */
  private calculateSize(value: T): number {
    try {
      return JSON.stringify(value).length * 2; // Rough estimate (UTF-16)
    } catch {
      return 1000; // Default size for non-serializable objects
    }
  }

  /**
   * Updates hit rate statistics
   */
  private updateHitRate(): void {
    const total = this.stats.hitCount + this.stats.missCount;
    this.stats.hitRate = total > 0 ? this.stats.hitCount / total : 0;
  }

  /**
   * Starts the cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Cleans up expired entries
   */
  private cleanup(): void {
    const keysToDelete: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        keysToDelete.push(key);
      }
    }
    
    for (const key of keysToDelete) {
      this.delete(key);
    }
  }

  /**
   * Persists an entry to disk
   */
  private async persistEntry(entry: CacheEntry<T>): Promise<void> {
    if (!this.config.diskCachePath) return;

    try {
      const filePath = path.join(this.config.diskCachePath, this.hashKey(entry.key));
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      
      let data = JSON.stringify(entry);
      
      if (this.config.enableCompression) {
        // Implement compression if needed
      }
      
      if (this.config.enableEncryption && this.config.encryptionKey) {
        // Implement encryption if needed
      }
      
      await fs.writeFile(filePath, data, 'utf-8');
    } catch (error) {
      console.error('Error persisting cache entry:', error);
    }
  }

  /**
   * Loads cache from disk
   */
  private async loadFromDisk(): Promise<void> {
    if (!this.config.diskCachePath) return;

    try {
      const files = await fs.readdir(this.config.diskCachePath);
      
      for (const file of files) {
        try {
          const filePath = path.join(this.config.diskCachePath, file);
          let data = await fs.readFile(filePath, 'utf-8');
          
          if (this.config.enableEncryption && this.config.encryptionKey) {
            // Implement decryption if needed
          }
          
          if (this.config.enableCompression) {
            // Implement decompression if needed
          }
          
          const entry: CacheEntry<T> = JSON.parse(data);
          
          if (!this.isExpired(entry)) {
            this.cache.set(entry.key, entry);
            this.stats.totalEntries++;
            this.stats.totalSize += entry.size || 0;
          }
        } catch (error) {
          console.error(`Error loading cache file ${file}:`, error);
        }
      }
    } catch (error) {
      console.error('Error loading cache from disk:', error);
    }
  }

  /**
   * Removes an entry from disk
   */
  private async removeFromDisk(key: string): Promise<void> {
    if (!this.config.diskCachePath) return;

    try {
      const filePath = path.join(this.config.diskCachePath, this.hashKey(key));
      await fs.unlink(filePath);
    } catch (error) {
      // File might not exist, which is fine
    }
  }

  /**
   * Hashes a key for use as filename
   */
  private hashKey(key: string): string {
    return createHash('sha256').update(key).digest('hex');
  }

  /**
   * Destroys the cache and cleans up resources
   */
  async destroy(): Promise<void> {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    if (this.config.persistToDisk) {
      // Save current state before destroying
      for (const [key, entry] of this.cache.entries()) {
        await this.persistEntry(entry);
      }
    }
    
    this.cache.clear();
  }
}

// Global cache instances
const globalCaches = new Map<string, AdvancedCache>();

/**
 * Gets or creates a named cache instance
 */
export function getCache<T = any>(
  name: string,
  config?: Partial<CacheConfig>,
  evictionStrategy?: EvictionStrategy
): AdvancedCache<T> {
  if (!globalCaches.has(name)) {
    globalCaches.set(name, new AdvancedCache<T>(config, evictionStrategy));
  }
  return globalCaches.get(name) as AdvancedCache<T>;
}

/**
 * Creates a cache decorator for methods
 */
export function cached<T>(
  cacheName: string,
  keyGenerator?: (...args: any[]) => string,
  ttl?: number
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const cache = getCache<T>(cacheName);

    descriptor.value = async function (...args: any[]): Promise<T> {
      const key = keyGenerator ? keyGenerator(...args) : `${propertyKey}_${JSON.stringify(args)}`;
      
      return cache.getOrSet(
        key,
        () => originalMethod.apply(this, args),
        ttl
      );
    };

    return descriptor;
  };
}
