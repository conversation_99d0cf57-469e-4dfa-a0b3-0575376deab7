{"total": {"lines": {"total": 15150, "covered": 406, "skipped": 0, "pct": 2.67}, "statements": {"total": 15150, "covered": 406, "skipped": 0, "pct": 2.67}, "functions": {"total": 126, "covered": 2, "skipped": 0, "pct": 1.58}, "branches": {"total": 126, "covered": 2, "skipped": 0, "pct": 1.58}, "branchesTrue": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\arien.tsx": {"lines": {"total": 225, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 225, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\nonInteractiveCli.ts": {"lines": {"total": 122, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 122, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\config\\auth.ts": {"lines": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\config\\built-in-mcp-servers.ts": {"lines": {"total": 406, "covered": 406, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 406, "covered": 406, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\config\\config.ts": {"lines": {"total": 314, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 314, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\config\\extension.ts": {"lines": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\config\\sandboxConfig.ts": {"lines": {"total": 74, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 74, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\config\\settings.ts": {"lines": {"total": 172, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 172, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\generated\\git-commit.ts": {"lines": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\App.tsx": {"lines": {"total": 733, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 733, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\colors.ts": {"lines": {"total": 43, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 43, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\constants.ts": {"lines": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\types.ts": {"lines": {"total": 24, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 24, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\AboutBox.tsx": {"lines": {"total": 95, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 95, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\ArienRespondingSpinner.tsx": {"lines": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\AsciiArt.ts": {"lines": {"total": 20, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 20, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\AuthDialog.tsx": {"lines": {"total": 83, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 83, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\AuthInProgress.tsx": {"lines": {"total": 38, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 38, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\AutoAcceptIndicator.tsx": {"lines": {"total": 36, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 36, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\ConsolePatcher.tsx": {"lines": {"total": 41, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 41, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\ConsoleSummaryDisplay.tsx": {"lines": {"total": 19, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 19, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\ContextSummaryDisplay.tsx": {"lines": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\DetailedMessagesDisplay.tsx": {"lines": {"total": 57, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 57, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\EditorSettingsDialog.tsx": {"lines": {"total": 133, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 133, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\Footer.tsx": {"lines": {"total": 91, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 91, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\Header.tsx": {"lines": {"total": 38, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 38, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\Help.tsx": {"lines": {"total": 118, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 118, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\HistoryItemDisplay.tsx": {"lines": {"total": 69, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 69, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\InputPrompt.tsx": {"lines": {"total": 397, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 397, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\LoadingIndicator.tsx": {"lines": {"total": 66, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 66, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\MemoryUsageDisplay.tsx": {"lines": {"total": 38, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 38, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\SessionSummaryDisplay.tsx": {"lines": {"total": 62, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 62, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\ShellModeIndicator.tsx": {"lines": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\ShowMoreLines.tsx": {"lines": {"total": 30, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 30, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\Stats.tsx": {"lines": {"total": 68, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 68, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\StatsDisplay.tsx": {"lines": {"total": 66, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 66, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\SuggestionsDisplay.tsx": {"lines": {"total": 71, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 71, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\ThemeDialog.tsx": {"lines": {"total": 185, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 185, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\Tips.tsx": {"lines": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\UpdateNotification.tsx": {"lines": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\messages\\ArienMessage.tsx": {"lines": {"total": 27, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 27, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\messages\\ArienMessageContent.tsx": {"lines": {"total": 20, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 20, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\messages\\CompressionMessage.tsx": {"lines": {"total": 28, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 28, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\messages\\DiffRenderer.tsx": {"lines": {"total": 244, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 244, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\messages\\ErrorMessage.tsx": {"lines": {"total": 32, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 32, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\messages\\InfoMessage.tsx": {"lines": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\messages\\ToolConfirmationMessage.tsx": {"lines": {"total": 185, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 185, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\messages\\ToolGroupMessage.tsx": {"lines": {"total": 86, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 86, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\messages\\ToolMessage.tsx": {"lines": {"total": 204, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 204, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\messages\\UserMessage.tsx": {"lines": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\messages\\UserShellMessage.tsx": {"lines": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\shared\\AnimatedIcon.tsx": {"lines": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\shared\\MaxSizedBox.tsx": {"lines": {"total": 360, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 360, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\shared\\ProgressBar.tsx": {"lines": {"total": 49, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 49, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\shared\\PromptIndicator.tsx": {"lines": {"total": 49, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 49, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\shared\\RadioButtonSelect.tsx": {"lines": {"total": 67, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 67, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\shared\\Separator.tsx": {"lines": {"total": 53, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 53, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\shared\\StatusBadge.tsx": {"lines": {"total": 49, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 49, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\shared\\index.ts": {"lines": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\components\\shared\\text-buffer.ts": {"lines": {"total": 1135, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 1135, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\contexts\\OverflowContext.tsx": {"lines": {"total": 56, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 56, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\contexts\\SessionContext.tsx": {"lines": {"total": 134, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 134, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\contexts\\StreamingContext.tsx": {"lines": {"total": 14, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 14, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\editors\\editorSettingsManager.ts": {"lines": {"total": 47, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 47, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\atCommandProcessor.ts": {"lines": {"total": 320, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 320, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\shellCommandProcessor.ts": {"lines": {"total": 250, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 250, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\slashCommandProcessor.ts": {"lines": {"total": 957, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 957, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useArienStream.ts": {"lines": {"total": 701, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 701, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useAuthCommand.ts": {"lines": {"total": 64, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 64, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useAutoAcceptIndicator.ts": {"lines": {"total": 33, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 33, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useBracketedPaste.ts": {"lines": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useCompletion.ts": {"lines": {"total": 353, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 353, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useConsoleMessages.ts": {"lines": {"total": 66, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 66, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useEditorSettings.ts": {"lines": {"total": 49, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 49, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useGitBranchName.ts": {"lines": {"total": 60, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 60, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useHistoryManager.ts": {"lines": {"total": 111, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 111, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useInputHistory.ts": {"lines": {"total": 80, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 80, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useKeypress.ts": {"lines": {"total": 66, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 66, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useLoadingIndicator.ts": {"lines": {"total": 43, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 43, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useLogger.ts": {"lines": {"total": 16, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 16, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\usePhraseCycler.ts": {"lines": {"total": 178, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 178, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\usePrivacySettings.ts": {"lines": {"total": 111, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 111, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useReactToolScheduler.ts": {"lines": {"total": 224, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 224, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useRefreshMemoryCommand.ts": {"lines": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useShellHistory.ts": {"lines": {"total": 87, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 87, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useShowMemoryCommand.ts": {"lines": {"total": 61, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 61, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useStateAndRef.ts": {"lines": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useTerminalSize.ts": {"lines": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useThemeCommand.ts": {"lines": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\hooks\\useTimer.ts": {"lines": {"total": 42, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 42, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\privacy\\ArienPrivacyNotice.tsx": {"lines": {"total": 37, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 37, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\privacy\\CloudFreePrivacyNotice.tsx": {"lines": {"total": 73, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 73, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\privacy\\CloudPaidPrivacyNotice.tsx": {"lines": {"total": 31, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 31, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\privacy\\PrivacyNotice.tsx": {"lines": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\ansi-light.ts": {"lines": {"total": 139, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 139, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\ansi.ts": {"lines": {"total": 143, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 143, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\atom-one-dark.ts": {"lines": {"total": 136, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 136, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\ayu-light.ts": {"lines": {"total": 128, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 128, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\ayu.ts": {"lines": {"total": 102, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 102, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\default-light.ts": {"lines": {"total": 100, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 100, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\default.ts": {"lines": {"total": 143, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 143, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\dracula.ts": {"lines": {"total": 113, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 113, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\github-dark.ts": {"lines": {"total": 136, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 136, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\github-light.ts": {"lines": {"total": 138, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 138, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\googlecode.ts": {"lines": {"total": 135, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 135, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\no-color.ts": {"lines": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\shades-of-purple.ts": {"lines": {"total": 256, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 256, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\theme-manager.ts": {"lines": {"total": 88, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 88, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\theme.ts": {"lines": {"total": 258, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 258, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\themes\\xcode.ts": {"lines": {"total": 143, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 143, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\utils\\CodeColorizer.tsx": {"lines": {"total": 118, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 118, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\utils\\MarkdownDisplay.tsx": {"lines": {"total": 399, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 399, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\utils\\commandUtils.ts": {"lines": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\utils\\errorParsing.ts": {"lines": {"total": 77, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 77, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\utils\\formatters.ts": {"lines": {"total": 42, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 42, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\utils\\markdownUtilities.ts": {"lines": {"total": 63, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 63, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\utils\\renderingCoordinator.ts": {"lines": {"total": 94, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 94, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\utils\\textUtils.ts": {"lines": {"total": 33, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 33, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\ui\\utils\\updateCheck.ts": {"lines": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\utils\\cleanup.ts": {"lines": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\utils\\package.ts": {"lines": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\utils\\readStdin.ts": {"lines": {"total": 29, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 29, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\utils\\sandbox.ts": {"lines": {"total": 665, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 665, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\utils\\startupWarnings.ts": {"lines": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli\\src\\utils\\version.ts": {"lines": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}}