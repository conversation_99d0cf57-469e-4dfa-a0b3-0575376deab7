------------------------------|---------|----------|---------|---------|----------------------
File                          | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s    
------------------------------|---------|----------|---------|---------|----------------------
All files                     |    4.82 |    18.18 |   10.14 |    4.82 |                      
 src                          |       0 |        0 |       0 |       0 |                      
  arien.tsx                   |       0 |        0 |       0 |       0 | 1-296                
  nonInteractiveCli.ts        |       0 |        0 |       0 |       0 | 1-156                
 src/config                   |   66.07 |    46.03 |   63.15 |   66.07 |                      
  auth.ts                     |       0 |        0 |       0 |       0 | 1-39                 
  built-in-mcp-servers.ts     |    99.5 |      100 |      50 |    99.5 | 461-462              
  config.ts                   |   85.66 |    54.16 |   81.81 |   85.66 | ...2,395-396,406-407 
  extension.ts                |       0 |        0 |       0 |       0 | 1-115                
  sandboxConfig.ts            |   51.35 |    18.18 |   66.66 |   51.35 | ...36,43,53-69,74-91 
  settings.ts                 |       0 |        0 |       0 |       0 | 1-265                
 src/generated                |       0 |        0 |       0 |       0 |                      
  git-commit.ts               |       0 |        0 |       0 |       0 | 1-9                  
 src/ui                       |       0 |        0 |       0 |       0 |                      
  App.tsx                     |       0 |        0 |       0 |       0 | 1-918                
  colors.ts                   |       0 |        0 |       0 |       0 | 1-50                 
  constants.ts                |       0 |        0 |       0 |       0 | 1-15                 
  types.ts                    |       0 |        0 |       0 |       0 | 1-157                
 src/ui/components            |       0 |        0 |       0 |       0 |                      
  AboutBox.tsx                |       0 |        0 |       0 |       0 | 1-122                
  ArienRespondingSpinner.tsx  |       0 |        0 |       0 |       0 | 1-37                 
  AsciiArt.ts                 |       0 |        0 |       0 |       0 | 1-36                 
  AuthDialog.tsx              |       0 |        0 |       0 |       0 | 1-107                
  AuthInProgress.tsx          |       0 |        0 |       0 |       0 | 1-57                 
  AutoAcceptIndicator.tsx     |       0 |        0 |       0 |       0 | 1-50                 
  ConsolePatcher.tsx          |       0 |        0 |       0 |       0 | 1-60                 
  ConsoleSummaryDisplay.tsx   |       0 |        0 |       0 |       0 | 1-35                 
  ContextSummaryDisplay.tsx   |       0 |        0 |       0 |       0 | 1-70                 
  DetailedMessagesDisplay.tsx |       0 |        0 |       0 |       0 | 1-82                 
  EditorSettingsDialog.tsx    |       0 |        0 |       0 |       0 | 1-168                
  Footer.tsx                  |       0 |        0 |       0 |       0 | 1-128                
  Header.tsx                  |       0 |        0 |       0 |       0 | 1-59                 
  Help.tsx                    |       0 |        0 |       0 |       0 | 1-172                
  HistoryItemDisplay.tsx      |       0 |        0 |       0 |       0 | 1-95                 
  InputPrompt.tsx             |       0 |        0 |       0 |       0 | 1-473                
  LoadingIndicator.tsx        |       0 |        0 |       0 |       0 | 1-97                 
  MemoryUsageDisplay.tsx      |       0 |        0 |       0 |       0 | 1-50                 
  SessionSummaryDisplay.tsx   |       0 |        0 |       0 |       0 | 1-88                 
  ShellModeIndicator.tsx      |       0 |        0 |       0 |       0 | 1-17                 
  ShowMoreLines.tsx           |       0 |        0 |       0 |       0 | 1-45                 
  Stats.tsx                   |       0 |        0 |       0 |       0 | 1-117                
  StatsDisplay.tsx            |       0 |        0 |       0 |       0 | 1-97                 
  SuggestionsDisplay.tsx      |       0 |        0 |       0 |       0 | 1-108                
  ThemeDialog.tsx             |       0 |        0 |       0 |       0 | 1-258                
  Tips.tsx                    |       0 |        0 |       0 |       0 | 1-51                 
  UpdateNotification.tsx      |       0 |        0 |       0 |       0 | 1-26                 
 src/ui/components/messages   |       0 |        0 |       0 |       0 |                      
  ArienMessage.tsx            |       0 |        0 |       0 |       0 | 1-45                 
  ArienMessageContent.tsx     |       0 |        0 |       0 |       0 | 1-45                 
  CompressionMessage.tsx      |       0 |        0 |       0 |       0 | 1-50                 
  DiffRenderer.tsx            |       0 |        0 |       0 |       0 | 1-312                
  ErrorMessage.tsx            |       0 |        0 |       0 |       0 | 1-49                 
  InfoMessage.tsx             |       0 |        0 |       0 |       0 | 1-40                 
  ToolConfirmationMessage.tsx |       0 |        0 |       0 |       0 | 1-250                
  ToolGroupMessage.tsx        |       0 |        0 |       0 |       0 | 1-123                
  ToolMessage.tsx             |       0 |        0 |       0 |       0 | 1-277                
  UserMessage.tsx             |       0 |        0 |       0 |       0 | 1-31                 
  UserShellMessage.tsx        |       0 |        0 |       0 |       0 | 1-25                 
 src/ui/components/shared     |       0 |        0 |       0 |       0 |                      
  AnimatedIcon.tsx            |       0 |        0 |       0 |       0 | 1-41                 
  MaxSizedBox.tsx             |       0 |        0 |       0 |       0 | 1-544                
  ProgressBar.tsx             |       0 |        0 |       0 |       0 | 1-78                 
  PromptIndicator.tsx         |       0 |        0 |       0 |       0 | 1-79                 
  RadioButtonSelect.tsx       |       0 |        0 |       0 |       0 | 1-142                
  Separator.tsx               |       0 |        0 |       0 |       0 | 1-92                 
  StatusBadge.tsx             |       0 |        0 |       0 |       0 | 1-85                 
  index.ts                    |       0 |        0 |       0 |       0 | 1-13                 
  text-buffer.ts              |       0 |        0 |       0 |       0 | 1-1415               
 src/ui/contexts              |       0 |        0 |       0 |       0 |                      
  OverflowContext.tsx         |       0 |        0 |       0 |       0 | 1-87                 
  SessionContext.tsx          |       0 |        0 |       0 |       0 | 1-204                
  StreamingContext.tsx        |       0 |        0 |       0 |       0 | 1-22                 
 src/ui/editors               |       0 |        0 |       0 |       0 |                      
  editorSettingsManager.ts    |       0 |        0 |       0 |       0 | 1-69                 
 src/ui/hooks                 |       0 |        0 |       0 |       0 |                      
  atCommandProcessor.ts       |       0 |        0 |       0 |       0 | 1-425                
  shellCommandProcessor.ts    |       0 |        0 |       0 |       0 | 1-348                
  slashCommandProcessor.ts    |       0 |        0 |       0 |       0 | 1-1098               
  useArienStream.ts           |       0 |        0 |       0 |       0 | 1-868                
  useAuthCommand.ts           |       0 |        0 |       0 |       0 | 1-87                 
  useAutoAcceptIndicator.ts   |       0 |        0 |       0 |       0 | 1-49                 
  useBracketedPaste.ts        |       0 |        0 |       0 |       0 | 1-37                 
  useCompletion.ts            |       0 |        0 |       0 |       0 | 1-449                
  useConsoleMessages.ts       |       0 |        0 |       0 |       0 | 1-89                 
  useEditorSettings.ts        |       0 |        0 |       0 |       0 | 1-75                 
  useGitBranchName.ts         |       0 |        0 |       0 |       0 | 1-79                 
  useHistoryManager.ts        |       0 |        0 |       0 |       0 | 1-178                
  useInputHistory.ts          |       0 |        0 |       0 |       0 | 1-111                
  useKeypress.ts              |       0 |        0 |       0 |       0 | 1-104                
  useLoadingIndicator.ts      |       0 |        0 |       0 |       0 | 1-57                 
  useLogger.ts                |       0 |        0 |       0 |       0 | 1-32                 
  usePhraseCycler.ts          |       0 |        0 |       0 |       0 | 1-200                
  usePrivacySettings.ts       |       0 |        0 |       0 |       0 | 1-135                
  useReactToolScheduler.ts    |       0 |        0 |       0 |       0 | 1-312                
  useRefreshMemoryCommand.ts  |       0 |        0 |       0 |       0 | 1-7                  
  useShellHistory.ts          |       0 |        0 |       0 |       0 | 1-103                
  useShowMemoryCommand.ts     |       0 |        0 |       0 |       0 | 1-75                 
  useStateAndRef.ts           |       0 |        0 |       0 |       0 | 1-36                 
  useTerminalSize.ts          |       0 |        0 |       0 |       0 | 1-48                 
  useThemeCommand.ts          |       0 |        0 |       0 |       0 | 1-116                
  useTimer.ts                 |       0 |        0 |       0 |       0 | 1-65                 
 src/ui/privacy               |       0 |        0 |       0 |       0 |                      
  ArienPrivacyNotice.tsx      |       0 |        0 |       0 |       0 | 1-58                 
  CloudFreePrivacyNotice.tsx  |       0 |        0 |       0 |       0 | 1-113                
  CloudPaidPrivacyNotice.tsx  |       0 |        0 |       0 |       0 | 1-55                 
  PrivacyNotice.tsx           |       0 |        0 |       0 |       0 | 1-41                 
 src/ui/themes                |       0 |        0 |       0 |       0 |                      
  ansi-light.ts               |       0 |        0 |       0 |       0 | 1-146                
  ansi.ts                     |       0 |        0 |       0 |       0 | 1-155                
  atom-one-dark.ts            |       0 |        0 |       0 |       0 | 1-143                
  ayu-light.ts                |       0 |        0 |       0 |       0 | 1-135                
  ayu.ts                      |       0 |        0 |       0 |       0 | 1-109                
  default-light.ts            |       0 |        0 |       0 |       0 | 1-106                
  default.ts                  |       0 |        0 |       0 |       0 | 1-149                
  dracula.ts                  |       0 |        0 |       0 |       0 | 1-120                
  github-dark.ts              |       0 |        0 |       0 |       0 | 1-143                
  github-light.ts             |       0 |        0 |       0 |       0 | 1-145                
  googlecode.ts               |       0 |        0 |       0 |       0 | 1-142                
  no-color.ts                 |       0 |        0 |       0 |       0 | 1-91                 
  shades-of-purple.ts         |       0 |        0 |       0 |       0 | 1-348                
  theme-manager.ts            |       0 |        0 |       0 |       0 | 1-125                
  theme.ts                    |       0 |        0 |       0 |       0 | 1-341                
  xcode.ts                    |       0 |        0 |       0 |       0 | 1-150                
 src/ui/utils                 |       0 |        0 |       0 |       0 |                      
  CodeColorizer.tsx           |       0 |        0 |       0 |       0 | 1-184                
  MarkdownDisplay.tsx         |       0 |        0 |       0 |       0 | 1-490                
  commandUtils.ts             |       0 |        0 |       0 |       0 | 1-26                 
  errorParsing.ts             |       0 |        0 |       0 |       0 | 1-106                
  formatters.ts               |       0 |        0 |       0 |       0 | 1-63                 
  markdownUtilities.ts        |       0 |        0 |       0 |       0 | 1-125                
  renderingCoordinator.ts     |       0 |        0 |       0 |       0 | 1-167                
  textUtils.ts                |       0 |        0 |       0 |       0 | 1-69                 
  updateCheck.ts              |       0 |        0 |       0 |       0 | 1-36                 
 src/utils                    |    2.52 |    45.45 |   33.33 |    2.52 |                      
  cleanup.ts                  |       0 |        0 |       0 |       0 | 1-19                 
  package.ts                  |   86.66 |       80 |     100 |   86.66 | 31-32                
  readStdin.ts                |       0 |        0 |       0 |       0 | 1-39                 
  sandbox.ts                  |       0 |        0 |       0 |       0 | 1-871                
  startupWarnings.ts          |       0 |        0 |       0 |       0 | 1-40                 
  version.ts                  |     100 |       50 |     100 |     100 | 11                   
------------------------------|---------|----------|---------|---------|----------------------
