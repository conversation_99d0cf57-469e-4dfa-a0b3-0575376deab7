/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { MCPServerConfig } from '@arien/arien-cli-core';
/**
 * Built-in MCP servers that are automatically available without manual configuration.
 * These servers have the lowest priority and can be overridden by extensions or user settings.
 */
export declare const BUILT_IN_MCP_SERVERS: Record<string, MCPServerConfig>;
/**
 * MCP servers that require API keys or special configuration.
 * These are not included by default but can be easily enabled by users.
 */
export declare const CONFIGURABLE_MCP_SERVERS: Record<string, MCPServerConfig>;
/**
 * Get the built-in MCP servers that should be automatically loaded.
 * This function can be extended in the future to include conditional logic
 * for enabling/disabling certain servers based on environment or user preferences.
 */
export declare function getBuiltInMcpServers(): Record<string, MCPServerConfig>;
/**
 * Get configurable MCP servers that require additional setup.
 * These are provided for reference but not automatically loaded.
 */
export declare function getConfigurableMcpServers(): Record<string, MCPServerConfig>;
