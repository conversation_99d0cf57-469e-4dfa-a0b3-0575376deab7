/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { getPerformanceMonitor } from '../telemetry/performanceMonitor.js';
import { getErrorHandler } from '../utils/advancedErrorHandling.js';
import { getCache } from '../utils/cacheSystem.js';

/**
 * Tool execution context
 */
export interface ToolExecutionContext {
  sessionId: string;
  userId?: string;
  workspaceRoot: string;
  environment: Record<string, string>;
  permissions: ToolPermissions;
  timeout: number;
  retryPolicy?: RetryPolicy;
}

/**
 * Tool permissions
 */
export interface ToolPermissions {
  allowFileSystem: boolean;
  allowNetwork: boolean;
  allowShell: boolean;
  allowedDomains?: string[];
  allowedPaths?: string[];
  maxFileSize?: number;
  maxExecutionTime?: number;
}

/**
 * Retry policy for tool execution
 */
export interface RetryPolicy {
  maxRetries: number;
  backoffMs: number;
  retryableErrors: string[];
}

/**
 * Tool execution result
 */
export interface ToolExecutionResult<T = any> {
  success: boolean;
  result?: T;
  error?: string;
  executionTime: number;
  retryCount: number;
  fromCache: boolean;
  metadata?: Record<string, any>;
}

/**
 * Tool definition
 */
export interface ToolDefinition {
  name: string;
  description: string;
  parameters: Record<string, any>;
  execute: (params: any, context: ToolExecutionContext) => Promise<any>;
  validate?: (params: any) => boolean | string;
  permissions?: Partial<ToolPermissions>;
  cacheable?: boolean;
  cacheKeyGenerator?: (params: any) => string;
  cacheTtl?: number;
}

/**
 * Tool execution statistics
 */
export interface ToolExecutionStats {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  cacheHitRate: number;
  mostUsedTools: Array<{ name: string; count: number }>;
  errorBreakdown: Record<string, number>;
}

/**
 * Advanced tool execution engine with caching, retries, and monitoring
 */
export class ToolExecutionEngine {
  private tools: Map<string, ToolDefinition> = new Map();
  private executionCache = getCache<any>('tool-execution');
  private performanceMonitor = getPerformanceMonitor();
  private errorHandler = getErrorHandler();
  private stats: ToolExecutionStats = {
    totalExecutions: 0,
    successfulExecutions: 0,
    failedExecutions: 0,
    averageExecutionTime: 0,
    cacheHitRate: 0,
    mostUsedTools: [],
    errorBreakdown: {},
  };

  /**
   * Registers a tool with the execution engine
   */
  registerTool(tool: ToolDefinition): void {
    this.tools.set(tool.name, tool);
    console.log(`Registered tool: ${tool.name}`);
  }

  /**
   * Unregisters a tool from the execution engine
   */
  unregisterTool(toolName: string): boolean {
    const removed = this.tools.delete(toolName);
    if (removed) {
      console.log(`Unregistered tool: ${toolName}`);
    }
    return removed;
  }

  /**
   * Gets a list of all registered tools
   */
  getRegisteredTools(): ToolDefinition[] {
    return Array.from(this.tools.values());
  }

  /**
   * Gets a specific tool by name
   */
  getTool(toolName: string): ToolDefinition | undefined {
    return this.tools.get(toolName);
  }

  /**
   * Executes a tool with the given parameters
   */
  async executeTool<T = any>(
    toolName: string,
    parameters: any,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult<T>> {
    const spanId = this.performanceMonitor.startSpan(`tool-execution-${toolName}`, undefined, {
      tool_name: toolName,
      session_id: context.sessionId,
    });

    const startTime = Date.now();
    let retryCount = 0;
    let fromCache = false;

    try {
      // Get tool definition
      const tool = this.tools.get(toolName);
      if (!tool) {
        throw new Error(`Tool '${toolName}' not found`);
      }

      // Validate parameters
      if (tool.validate) {
        const validationResult = tool.validate(parameters);
        if (validationResult !== true) {
          throw new Error(`Parameter validation failed: ${validationResult}`);
        }
      }

      // Check permissions
      this.checkPermissions(tool, context);

      // Check cache if tool is cacheable
      let result: T | undefined;
      if (tool.cacheable) {
        const cacheKey = this.generateCacheKey(toolName, parameters, tool);
        result = await this.executionCache.get(cacheKey);
        if (result !== null) {
          fromCache = true;
          this.updateStats(toolName, true, Date.now() - startTime, fromCache);
          this.performanceMonitor.endSpan(spanId, { cache_hit: 'true' });
          return {
            success: true,
            result,
            executionTime: Date.now() - startTime,
            retryCount: 0,
            fromCache: true,
          };
        }
      }

      // Execute tool with retry logic
      const retryPolicy = context.retryPolicy || { maxRetries: 0, backoffMs: 1000, retryableErrors: [] };
      
      while (retryCount <= retryPolicy.maxRetries) {
        try {
          result = await this.executeWithTimeout(tool, parameters, context);
          
          // Cache result if tool is cacheable
          if (tool.cacheable && result !== undefined) {
            const cacheKey = this.generateCacheKey(toolName, parameters, tool);
            const ttl = tool.cacheTtl || 5 * 60 * 1000; // 5 minutes default
            await this.executionCache.set(cacheKey, result, ttl);
          }

          this.updateStats(toolName, true, Date.now() - startTime, fromCache);
          this.performanceMonitor.endSpan(spanId, { 
            success: 'true',
            retry_count: retryCount.toString(),
          });

          return {
            success: true,
            result,
            executionTime: Date.now() - startTime,
            retryCount,
            fromCache,
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          
          // Check if error is retryable
          if (retryCount < retryPolicy.maxRetries && this.isRetryableError(errorMessage, retryPolicy)) {
            retryCount++;
            console.log(`Tool execution failed, retrying (${retryCount}/${retryPolicy.maxRetries}): ${errorMessage}`);
            await this.delay(retryPolicy.backoffMs * retryCount);
            continue;
          }

          // Handle non-retryable error or max retries exceeded
          await this.errorHandler.handleError(error as Error, {
            tool_name: toolName,
            parameters,
            retry_count: retryCount,
          });

          this.updateStats(toolName, false, Date.now() - startTime, fromCache, errorMessage);
          this.performanceMonitor.endSpan(spanId, { 
            success: 'false',
            error: errorMessage,
            retry_count: retryCount.toString(),
          });

          return {
            success: false,
            error: errorMessage,
            executionTime: Date.now() - startTime,
            retryCount,
            fromCache,
          };
        }
      }

      // This should never be reached, but just in case
      throw new Error('Unexpected execution flow');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.updateStats(toolName, false, Date.now() - startTime, fromCache, errorMessage);
      this.performanceMonitor.endSpan(spanId, { 
        success: 'false',
        error: errorMessage,
      });

      return {
        success: false,
        error: errorMessage,
        executionTime: Date.now() - startTime,
        retryCount,
        fromCache,
      };
    }
  }

  /**
   * Executes multiple tools in parallel
   */
  async executeToolsParallel<T = any>(
    executions: Array<{
      toolName: string;
      parameters: any;
      context: ToolExecutionContext;
    }>
  ): Promise<ToolExecutionResult<T>[]> {
    const promises = executions.map(({ toolName, parameters, context }) =>
      this.executeTool<T>(toolName, parameters, context)
    );

    return Promise.all(promises);
  }

  /**
   * Executes multiple tools in sequence
   */
  async executeToolsSequential<T = any>(
    executions: Array<{
      toolName: string;
      parameters: any;
      context: ToolExecutionContext;
    }>
  ): Promise<ToolExecutionResult<T>[]> {
    const results: ToolExecutionResult<T>[] = [];

    for (const { toolName, parameters, context } of executions) {
      const result = await this.executeTool<T>(toolName, parameters, context);
      results.push(result);
      
      // Stop execution if a tool fails (optional behavior)
      if (!result.success) {
        console.warn(`Tool execution failed: ${toolName}, stopping sequence`);
        break;
      }
    }

    return results;
  }

  /**
   * Gets execution statistics
   */
  getExecutionStats(): ToolExecutionStats {
    return { ...this.stats };
  }

  /**
   * Clears execution cache
   */
  async clearCache(): Promise<void> {
    await this.executionCache.clear();
  }

  /**
   * Gets cache statistics
   */
  getCacheStats() {
    return this.executionCache.getStats();
  }

  /**
   * Executes a tool with timeout
   */
  private async executeWithTimeout(
    tool: ToolDefinition,
    parameters: any,
    context: ToolExecutionContext
  ): Promise<any> {
    const timeout = context.timeout || 30000; // 30 seconds default

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Tool execution timed out after ${timeout}ms`));
      }, timeout);

      tool.execute(parameters, context)
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Checks if the tool execution is allowed based on permissions
   */
  private checkPermissions(tool: ToolDefinition, context: ToolExecutionContext): void {
    const requiredPermissions = tool.permissions || {};
    const userPermissions = context.permissions;

    if (requiredPermissions.allowFileSystem && !userPermissions.allowFileSystem) {
      throw new Error('Tool requires file system access but permission is denied');
    }

    if (requiredPermissions.allowNetwork && !userPermissions.allowNetwork) {
      throw new Error('Tool requires network access but permission is denied');
    }

    if (requiredPermissions.allowShell && !userPermissions.allowShell) {
      throw new Error('Tool requires shell access but permission is denied');
    }

    // Additional permission checks can be added here
  }

  /**
   * Generates a cache key for tool execution
   */
  private generateCacheKey(toolName: string, parameters: any, tool: ToolDefinition): string {
    if (tool.cacheKeyGenerator) {
      return `${toolName}_${tool.cacheKeyGenerator(parameters)}`;
    }
    
    // Default cache key generation
    const paramHash = Buffer.from(JSON.stringify(parameters)).toString('base64');
    return `${toolName}_${paramHash}`;
  }

  /**
   * Checks if an error is retryable
   */
  private isRetryableError(errorMessage: string, retryPolicy: RetryPolicy): boolean {
    return retryPolicy.retryableErrors.some(pattern => 
      errorMessage.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  /**
   * Updates execution statistics
   */
  private updateStats(
    toolName: string,
    success: boolean,
    executionTime: number,
    fromCache: boolean,
    error?: string
  ): void {
    this.stats.totalExecutions++;
    
    if (success) {
      this.stats.successfulExecutions++;
    } else {
      this.stats.failedExecutions++;
      if (error) {
        this.stats.errorBreakdown[error] = (this.stats.errorBreakdown[error] || 0) + 1;
      }
    }

    // Update average execution time
    this.stats.averageExecutionTime = 
      (this.stats.averageExecutionTime * (this.stats.totalExecutions - 1) + executionTime) / 
      this.stats.totalExecutions;

    // Update cache hit rate
    const cacheHits = fromCache ? 1 : 0;
    this.stats.cacheHitRate = 
      (this.stats.cacheHitRate * (this.stats.totalExecutions - 1) + cacheHits) / 
      this.stats.totalExecutions;

    // Update most used tools
    const existingTool = this.stats.mostUsedTools.find(t => t.name === toolName);
    if (existingTool) {
      existingTool.count++;
    } else {
      this.stats.mostUsedTools.push({ name: toolName, count: 1 });
    }
    
    // Sort and keep top 10
    this.stats.mostUsedTools.sort((a, b) => b.count - a.count);
    this.stats.mostUsedTools = this.stats.mostUsedTools.slice(0, 10);
  }

  /**
   * Utility function to delay execution
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Global tool execution engine instance
let globalToolEngine: ToolExecutionEngine | null = null;

/**
 * Gets the global tool execution engine instance
 */
export function getToolExecutionEngine(): ToolExecutionEngine {
  if (!globalToolEngine) {
    globalToolEngine = new ToolExecutionEngine();
  }
  return globalToolEngine;
}

/**
 * Initializes the global tool execution engine
 */
export function initializeToolExecutionEngine(): ToolExecutionEngine {
  globalToolEngine = new ToolExecutionEngine();
  return globalToolEngine;
}
