/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { FileDiscoveryService, FilterFilesOptions } from './fileDiscoveryService.js';
import { getCache } from '../utils/cacheSystem.js';
import { getPerformanceMonitor } from '../telemetry/performanceMonitor.js';

/**
 * File metadata information
 */
export interface FileMetadata {
  path: string;
  size: number;
  lastModified: number;
  isDirectory: boolean;
  extension?: string;
  mimeType?: string;
  encoding?: string;
  lineCount?: number;
  language?: string;
}

/**
 * Search criteria for file discovery
 */
export interface FileSearchCriteria {
  patterns?: string[];
  extensions?: string[];
  languages?: string[];
  minSize?: number;
  maxSize?: number;
  modifiedAfter?: Date;
  modifiedBefore?: Date;
  includeHidden?: boolean;
  maxDepth?: number;
  followSymlinks?: boolean;
}

/**
 * File discovery result
 */
export interface FileDiscoveryResult {
  files: FileMetadata[];
  totalFiles: number;
  totalSize: number;
  searchTime: number;
  fromCache: boolean;
}

/**
 * Enhanced file discovery service with caching, metadata, and advanced search
 */
export class EnhancedFileDiscoveryService extends FileDiscoveryService {
  private cache = getCache<FileDiscoveryResult>('file-discovery');
  private metadataCache = getCache<FileMetadata>('file-metadata');
  private performanceMonitor = getPerformanceMonitor();

  private static readonly LANGUAGE_EXTENSIONS: Record<string, string[]> = {
    javascript: ['.js', '.jsx', '.mjs', '.cjs'],
    typescript: ['.ts', '.tsx', '.d.ts'],
    python: ['.py', '.pyx', '.pyi', '.pyw'],
    java: ['.java', '.class', '.jar'],
    csharp: ['.cs', '.csx', '.vb'],
    cpp: ['.cpp', '.cc', '.cxx', '.c++', '.hpp', '.h++'],
    c: ['.c', '.h'],
    go: ['.go'],
    rust: ['.rs'],
    php: ['.php', '.phtml', '.php3', '.php4', '.php5'],
    ruby: ['.rb', '.rbw', '.rake', '.gemspec'],
    swift: ['.swift'],
    kotlin: ['.kt', '.kts'],
    scala: ['.scala', '.sc'],
    html: ['.html', '.htm', '.xhtml'],
    css: ['.css', '.scss', '.sass', '.less'],
    json: ['.json', '.jsonc', '.json5'],
    xml: ['.xml', '.xsd', '.xsl', '.xslt'],
    yaml: ['.yaml', '.yml'],
    markdown: ['.md', '.markdown', '.mdown', '.mkd'],
    sql: ['.sql', '.mysql', '.pgsql'],
    shell: ['.sh', '.bash', '.zsh', '.fish'],
    powershell: ['.ps1', '.psm1', '.psd1'],
    dockerfile: ['Dockerfile', '.dockerfile'],
    makefile: ['Makefile', 'makefile', '.mk'],
  };

  private static readonly MIME_TYPES: Record<string, string> = {
    '.js': 'application/javascript',
    '.ts': 'application/typescript',
    '.py': 'text/x-python',
    '.java': 'text/x-java-source',
    '.cs': 'text/x-csharp',
    '.cpp': 'text/x-c++src',
    '.c': 'text/x-csrc',
    '.go': 'text/x-go',
    '.rs': 'text/x-rust',
    '.php': 'application/x-php',
    '.rb': 'application/x-ruby',
    '.html': 'text/html',
    '.css': 'text/css',
    '.json': 'application/json',
    '.xml': 'application/xml',
    '.yaml': 'application/x-yaml',
    '.md': 'text/markdown',
    '.sql': 'application/sql',
    '.sh': 'application/x-sh',
    '.txt': 'text/plain',
    '.pdf': 'application/pdf',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
  };

  /**
   * Discovers files with advanced search criteria and caching
   */
  async discoverFiles(
    rootPath: string,
    criteria: FileSearchCriteria = {},
    options: FilterFilesOptions = {}
  ): Promise<FileDiscoveryResult> {
    const spanId = this.performanceMonitor.startSpan('file-discovery', undefined, {
      root_path: rootPath,
      criteria: JSON.stringify(criteria),
    });

    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(rootPath, criteria, options);

    try {
      // Check cache first
      const cached = await this.cache.get(cacheKey);
      if (cached) {
        this.performanceMonitor.endSpan(spanId, { cache_hit: 'true' });
        return { ...cached, fromCache: true };
      }

      // Perform file discovery
      const files = await this.performFileDiscovery(rootPath, criteria, options);
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);
      const searchTime = Date.now() - startTime;

      const result: FileDiscoveryResult = {
        files,
        totalFiles: files.length,
        totalSize,
        searchTime,
        fromCache: false,
      };

      // Cache the result
      await this.cache.set(cacheKey, result, 5 * 60 * 1000); // 5 minutes TTL

      this.performanceMonitor.endSpan(spanId, { 
        cache_hit: 'false',
        files_found: files.length.toString(),
      });

      return result;
    } catch (error) {
      this.performanceMonitor.endSpan(spanId, { 
        error: error instanceof Error ? error.message : 'unknown',
      });
      throw error;
    }
  }

  /**
   * Gets detailed metadata for a file
   */
  async getFileMetadata(filePath: string): Promise<FileMetadata | null> {
    const cacheKey = `metadata_${filePath}`;
    
    // Check cache first
    const cached = await this.metadataCache.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const stats = await fs.stat(filePath);
      const extension = path.extname(filePath).toLowerCase();
      const language = this.detectLanguage(filePath);
      const mimeType = this.getMimeType(extension);

      let lineCount: number | undefined;
      let encoding: string | undefined;

      // For text files, get additional metadata
      if (this.isTextFile(extension)) {
        try {
          const content = await fs.readFile(filePath, 'utf-8');
          lineCount = content.split('\n').length;
          encoding = 'utf-8';
        } catch {
          // File might be binary or have encoding issues
          encoding = 'binary';
        }
      }

      const metadata: FileMetadata = {
        path: filePath,
        size: stats.size,
        lastModified: stats.mtime.getTime(),
        isDirectory: stats.isDirectory(),
        extension: extension || undefined,
        mimeType,
        encoding,
        lineCount,
        language,
      };

      // Cache metadata for 10 minutes
      await this.metadataCache.set(cacheKey, metadata, 10 * 60 * 1000);

      return metadata;
    } catch (error) {
      console.error(`Error getting metadata for ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Searches for files by content
   */
  async searchFileContent(
    rootPath: string,
    searchTerm: string,
    criteria: FileSearchCriteria = {}
  ): Promise<Array<{
    file: FileMetadata;
    matches: Array<{ line: number; content: string; column: number }>;
  }>> {
    const spanId = this.performanceMonitor.startSpan('content-search', undefined, {
      root_path: rootPath,
      search_term: searchTerm,
    });

    try {
      const discovery = await this.discoverFiles(rootPath, criteria);
      const results: Array<{
        file: FileMetadata;
        matches: Array<{ line: number; content: string; column: number }>;
      }> = [];

      for (const file of discovery.files) {
        if (file.isDirectory || !this.isTextFile(file.extension || '')) {
          continue;
        }

        try {
          const content = await fs.readFile(file.path, 'utf-8');
          const lines = content.split('\n');
          const matches: Array<{ line: number; content: string; column: number }> = [];

          for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            let columnIndex = 0;
            
            while ((columnIndex = line.indexOf(searchTerm, columnIndex)) !== -1) {
              matches.push({
                line: i + 1,
                content: line,
                column: columnIndex + 1,
              });
              columnIndex += searchTerm.length;
            }
          }

          if (matches.length > 0) {
            results.push({ file, matches });
          }
        } catch (error) {
          console.error(`Error searching content in ${file.path}:`, error);
        }
      }

      this.performanceMonitor.endSpan(spanId, {
        files_searched: discovery.files.length.toString(),
        matches_found: results.length.toString(),
      });

      return results;
    } catch (error) {
      this.performanceMonitor.endSpan(spanId, {
        error: error instanceof Error ? error.message : 'unknown',
      });
      throw error;
    }
  }

  /**
   * Gets file statistics for a directory
   */
  async getDirectoryStats(dirPath: string): Promise<{
    totalFiles: number;
    totalDirectories: number;
    totalSize: number;
    languageBreakdown: Record<string, number>;
    extensionBreakdown: Record<string, number>;
    largestFiles: Array<{ path: string; size: number }>;
  }> {
    const discovery = await this.discoverFiles(dirPath);
    const stats = {
      totalFiles: 0,
      totalDirectories: 0,
      totalSize: 0,
      languageBreakdown: {} as Record<string, number>,
      extensionBreakdown: {} as Record<string, number>,
      largestFiles: [] as Array<{ path: string; size: number }>,
    };

    for (const file of discovery.files) {
      if (file.isDirectory) {
        stats.totalDirectories++;
      } else {
        stats.totalFiles++;
        stats.totalSize += file.size;

        if (file.language) {
          stats.languageBreakdown[file.language] = (stats.languageBreakdown[file.language] || 0) + 1;
        }

        if (file.extension) {
          stats.extensionBreakdown[file.extension] = (stats.extensionBreakdown[file.extension] || 0) + 1;
        }

        stats.largestFiles.push({ path: file.path, size: file.size });
      }
    }

    // Sort largest files
    stats.largestFiles.sort((a, b) => b.size - a.size);
    stats.largestFiles = stats.largestFiles.slice(0, 10);

    return stats;
  }

  /**
   * Performs the actual file discovery
   */
  private async performFileDiscovery(
    rootPath: string,
    criteria: FileSearchCriteria,
    options: FilterFilesOptions
  ): Promise<FileMetadata[]> {
    const files: FileMetadata[] = [];
    const maxDepth = criteria.maxDepth || 10;

    const traverse = async (currentPath: string, depth: number = 0): Promise<void> => {
      if (depth > maxDepth) return;

      try {
        const entries = await fs.readdir(currentPath, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(currentPath, entry.name);
          const relativePath = path.relative(rootPath, fullPath);

          // Skip hidden files unless explicitly included
          if (!criteria.includeHidden && entry.name.startsWith('.')) {
            continue;
          }

          // Apply filtering
          if (!this.shouldIncludeFile(relativePath, options)) {
            continue;
          }

          if (entry.isDirectory()) {
            const metadata = await this.getFileMetadata(fullPath);
            if (metadata && this.matchesCriteria(metadata, criteria)) {
              files.push(metadata);
            }
            await traverse(fullPath, depth + 1);
          } else if (entry.isFile() || (criteria.followSymlinks && entry.isSymbolicLink())) {
            const metadata = await this.getFileMetadata(fullPath);
            if (metadata && this.matchesCriteria(metadata, criteria)) {
              files.push(metadata);
            }
          }
        }
      } catch (error) {
        console.error(`Error reading directory ${currentPath}:`, error);
      }
    };

    await traverse(rootPath);
    return files;
  }

  /**
   * Checks if a file matches the search criteria
   */
  private matchesCriteria(file: FileMetadata, criteria: FileSearchCriteria): boolean {
    // Size filters
    if (criteria.minSize !== undefined && file.size < criteria.minSize) {
      return false;
    }
    if (criteria.maxSize !== undefined && file.size > criteria.maxSize) {
      return false;
    }

    // Date filters
    if (criteria.modifiedAfter && file.lastModified < criteria.modifiedAfter.getTime()) {
      return false;
    }
    if (criteria.modifiedBefore && file.lastModified > criteria.modifiedBefore.getTime()) {
      return false;
    }

    // Extension filters
    if (criteria.extensions && criteria.extensions.length > 0) {
      if (!file.extension || !criteria.extensions.includes(file.extension)) {
        return false;
      }
    }

    // Language filters
    if (criteria.languages && criteria.languages.length > 0) {
      if (!file.language || !criteria.languages.includes(file.language)) {
        return false;
      }
    }

    // Pattern filters
    if (criteria.patterns && criteria.patterns.length > 0) {
      const fileName = path.basename(file.path);
      const matchesPattern = criteria.patterns.some(pattern => {
        const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
        return regex.test(fileName);
      });
      if (!matchesPattern) {
        return false;
      }
    }

    return true;
  }

  /**
   * Detects the programming language of a file
   */
  private detectLanguage(filePath: string): string | undefined {
    const extension = path.extname(filePath).toLowerCase();
    const basename = path.basename(filePath).toLowerCase();

    // Check by extension
    for (const [language, extensions] of Object.entries(EnhancedFileDiscoveryService.LANGUAGE_EXTENSIONS)) {
      if (extensions.includes(extension) || extensions.includes(basename)) {
        return language;
      }
    }

    return undefined;
  }

  /**
   * Gets the MIME type for a file extension
   */
  private getMimeType(extension: string): string | undefined {
    return EnhancedFileDiscoveryService.MIME_TYPES[extension.toLowerCase()];
  }

  /**
   * Checks if a file is a text file
   */
  private isTextFile(extension: string): boolean {
    const textExtensions = [
      '.txt', '.md', '.js', '.ts', '.py', '.java', '.cs', '.cpp', '.c', '.go', '.rs',
      '.php', '.rb', '.html', '.css', '.json', '.xml', '.yaml', '.yml', '.sql', '.sh',
      '.ps1', '.bat', '.cmd', '.ini', '.cfg', '.conf', '.log', '.csv', '.tsv',
    ];
    return textExtensions.includes(extension.toLowerCase());
  }

  /**
   * Generates a cache key for the search criteria
   */
  private generateCacheKey(
    rootPath: string,
    criteria: FileSearchCriteria,
    options: FilterFilesOptions
  ): string {
    const key = {
      rootPath,
      criteria,
      options,
    };
    return `discovery_${Buffer.from(JSON.stringify(key)).toString('base64')}`;
  }

  /**
   * Checks if a file should be included based on filtering options
   */
  private shouldIncludeFile(relativePath: string, options: FilterFilesOptions): boolean {
    const filteredFiles = this.filterFiles([relativePath], options);
    return filteredFiles.length > 0;
  }
}
