<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="6" failures="0" errors="0" time="0.0826616">
    <testsuite name="src/config/mcp-integration.test.ts" timestamp="2025-07-02T08:24:01.495Z" hostname="Ajayk" tests="6" failures="0" errors="0" skipped="0" time="0.0826616">
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should automatically load built-in MCP servers without any configuration" time="0.0569015">
            <system-out>
[DEBUG] Loading 12 built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Filesystem&quot; from built-in.
[DEBUG] Adding MCP server &quot;Fetch&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Calculator&quot; from built-in.
[DEBUG] Adding MCP server &quot;QR Code&quot; from built-in.
[DEBUG] Adding MCP server &quot;DuckDuckGo Search&quot; from built-in.
[DEBUG] Adding MCP server &quot;Docker&quot; from built-in.
[DEBUG] Total MCP servers configured: 12

            </system-out>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should respect the enableBuiltInMcpServers setting when disabled" time="0.0059362">
            <system-out>
[DEBUG] Built-in MCP servers are disabled by user configuration.
[DEBUG] Total MCP servers configured: 0

            </system-out>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should allow user configuration to override built-in servers" time="0.0049657">
            <system-out>
[DEBUG] Loading 12 built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Filesystem&quot; from built-in.
[DEBUG] Adding MCP server &quot;Fetch&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Calculator&quot; from built-in.
[DEBUG] Adding MCP server &quot;QR Code&quot; from built-in.
[DEBUG] Adding MCP server &quot;DuckDuckGo Search&quot; from built-in.
[DEBUG] Adding MCP server &quot;Docker&quot; from built-in.
[DEBUG] Loading 2 MCP servers from user settings.
[DEBUG] Overriding MCP server &quot;Context 7&quot; with user settings configuration.
[DEBUG] Adding MCP server &quot;Custom Server&quot; from user settings.
[DEBUG] Total MCP servers configured: 13

            </system-out>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should allow extension configuration to override built-in servers" time="0.005097">
            <system-out>
[DEBUG] Loading 12 built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Filesystem&quot; from built-in.
[DEBUG] Adding MCP server &quot;Fetch&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Calculator&quot; from built-in.
[DEBUG] Adding MCP server &quot;QR Code&quot; from built-in.
[DEBUG] Adding MCP server &quot;DuckDuckGo Search&quot; from built-in.
[DEBUG] Adding MCP server &quot;Docker&quot; from built-in.
[DEBUG] Loading 2 MCP servers from extension &quot;test-extension&quot;.
[DEBUG] Overriding MCP server &quot;Git&quot; with extension &quot;test-extension&quot; configuration.
[DEBUG] Adding MCP server &quot;Extension Server&quot; from extension &quot;test-extension&quot;.
[DEBUG] Total MCP servers configured: 13

            </system-out>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should maintain correct priority: user &gt; extension &gt; built-in" time="0.004233">
            <system-out>
[DEBUG] Loading 12 built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Filesystem&quot; from built-in.
[DEBUG] Adding MCP server &quot;Fetch&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Calculator&quot; from built-in.
[DEBUG] Adding MCP server &quot;QR Code&quot; from built-in.
[DEBUG] Adding MCP server &quot;DuckDuckGo Search&quot; from built-in.
[DEBUG] Adding MCP server &quot;Docker&quot; from built-in.
[DEBUG] Loading 2 MCP servers from extension &quot;test-extension&quot;.
[DEBUG] Overriding MCP server &quot;Calculator&quot; with extension &quot;test-extension&quot; configuration.
[DEBUG] Overriding MCP server &quot;Memory&quot; with extension &quot;test-extension&quot; configuration.
[DEBUG] Loading 1 MCP servers from user settings.
[DEBUG] Overriding MCP server &quot;Calculator&quot; with user settings configuration.
[DEBUG] Total MCP servers configured: 12

            </system-out>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should handle mixed configuration scenarios gracefully" time="0.0038774">
            <system-out>
[DEBUG] Loading 12 built-in MCP servers.
[DEBUG] Adding MCP server &quot;Context 7&quot; from built-in.
[DEBUG] Adding MCP server &quot;Playwright&quot; from built-in.
[DEBUG] Adding MCP server &quot;Sequential thinking&quot; from built-in.
[DEBUG] Adding MCP server &quot;Filesystem&quot; from built-in.
[DEBUG] Adding MCP server &quot;Fetch&quot; from built-in.
[DEBUG] Adding MCP server &quot;Memory&quot; from built-in.
[DEBUG] Adding MCP server &quot;Time&quot; from built-in.
[DEBUG] Adding MCP server &quot;Git&quot; from built-in.
[DEBUG] Adding MCP server &quot;Calculator&quot; from built-in.
[DEBUG] Adding MCP server &quot;QR Code&quot; from built-in.
[DEBUG] Adding MCP server &quot;DuckDuckGo Search&quot; from built-in.
[DEBUG] Adding MCP server &quot;Docker&quot; from built-in.
[DEBUG] Loading 1 MCP servers from extension &quot;ext1&quot;.
[DEBUG] Adding MCP server &quot;Ext Server 1&quot; from extension &quot;ext1&quot;.
[DEBUG] Loading 1 MCP servers from extension &quot;ext2&quot;.
[DEBUG] Adding MCP server &quot;Ext Server 2&quot; from extension &quot;ext2&quot;.
[DEBUG] Loading 2 MCP servers from user settings.
[DEBUG] Adding MCP server &quot;User Server 1&quot; from user settings.
[DEBUG] Adding MCP server &quot;User Server 2&quot; from user settings.
[DEBUG] Total MCP servers configured: 16

            </system-out>
        </testcase>
    </testsuite>
</testsuites>
