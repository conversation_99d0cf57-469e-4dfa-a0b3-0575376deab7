/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Validation error details
 */
export interface ValidationError {
  path: string;
  message: string;
  value?: any;
  expectedType?: string;
  constraint?: string;
}

/**
 * Validation result
 */
export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

/**
 * Schema definition for validation
 */
export interface Schema {
  type?: 'object' | 'array' | 'string' | 'number' | 'boolean' | 'null';
  properties?: Record<string, Schema>;
  items?: Schema;
  required?: string[];
  additionalProperties?: boolean | Schema;
  minLength?: number;
  maxLength?: number;
  minimum?: number;
  maximum?: number;
  pattern?: string;
  enum?: any[];
  format?: 'email' | 'uri' | 'date' | 'time' | 'datetime' | 'uuid' | 'ipv4' | 'ipv6';
  oneOf?: Schema[];
  anyOf?: Schema[];
  allOf?: Schema[];
  not?: Schema;
  default?: any;
  description?: string;
}

/**
 * Advanced schema validator with comprehensive validation capabilities
 */
export class AdvancedSchemaValidator {
  private static formatValidators: Record<string, (value: string) => boolean> = {
    email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    uri: (value) => {
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    },
    date: (value) => /^\d{4}-\d{2}-\d{2}$/.test(value) && !isNaN(Date.parse(value)),
    time: (value) => /^\d{2}:\d{2}:\d{2}(\.\d{3})?$/.test(value),
    datetime: (value) => /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/.test(value) && !isNaN(Date.parse(value)),
    uuid: (value) => /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(value),
    ipv4: (value) => /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(value),
    ipv6: (value) => /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/.test(value),
  };

  /**
   * Validates data against a schema
   */
  static validate(data: unknown, schema: Schema): ValidationResult {
    const errors: ValidationError[] = [];
    this.validateValue(data, schema, '', errors);
    
    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validates data and throws an error if invalid
   */
  static validateOrThrow(data: unknown, schema: Schema): void {
    const result = this.validate(data, schema);
    if (!result.valid) {
      const errorMessages = result.errors.map(e => `${e.path}: ${e.message}`).join(', ');
      throw new Error(`Validation failed: ${errorMessages}`);
    }
  }

  /**
   * Validates a specific value against a schema
   */
  private static validateValue(
    value: unknown,
    schema: Schema,
    path: string,
    errors: ValidationError[]
  ): void {
    // Handle null values
    if (value === null) {
      if (schema.type && schema.type !== 'null') {
        errors.push({
          path,
          message: `Expected ${schema.type}, got null`,
          value,
          expectedType: schema.type,
        });
      }
      return;
    }

    // Handle undefined values
    if (value === undefined) {
      errors.push({
        path,
        message: 'Value is undefined',
        value,
      });
      return;
    }

    // Type validation
    if (schema.type) {
      if (!this.validateType(value, schema.type)) {
        errors.push({
          path,
          message: `Expected ${schema.type}, got ${typeof value}`,
          value,
          expectedType: schema.type,
        });
        return;
      }
    }

    // Enum validation
    if (schema.enum && !schema.enum.includes(value)) {
      errors.push({
        path,
        message: `Value must be one of: ${schema.enum.join(', ')}`,
        value,
        constraint: 'enum',
      });
    }

    // Type-specific validations
    switch (schema.type) {
      case 'string':
        this.validateString(value as string, schema, path, errors);
        break;
      case 'number':
        this.validateNumber(value as number, schema, path, errors);
        break;
      case 'object':
        this.validateObject(value as Record<string, unknown>, schema, path, errors);
        break;
      case 'array':
        this.validateArray(value as unknown[], schema, path, errors);
        break;
    }

    // Schema composition validations
    if (schema.oneOf) {
      this.validateOneOf(value, schema.oneOf, path, errors);
    }
    if (schema.anyOf) {
      this.validateAnyOf(value, schema.anyOf, path, errors);
    }
    if (schema.allOf) {
      this.validateAllOf(value, schema.allOf, path, errors);
    }
    if (schema.not) {
      this.validateNot(value, schema.not, path, errors);
    }
  }

  /**
   * Validates basic type
   */
  private static validateType(value: unknown, type: string): boolean {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'array':
        return Array.isArray(value);
      case 'null':
        return value === null;
      default:
        return true;
    }
  }

  /**
   * Validates string values
   */
  private static validateString(
    value: string,
    schema: Schema,
    path: string,
    errors: ValidationError[]
  ): void {
    if (schema.minLength !== undefined && value.length < schema.minLength) {
      errors.push({
        path,
        message: `String must be at least ${schema.minLength} characters long`,
        value,
        constraint: 'minLength',
      });
    }

    if (schema.maxLength !== undefined && value.length > schema.maxLength) {
      errors.push({
        path,
        message: `String must be at most ${schema.maxLength} characters long`,
        value,
        constraint: 'maxLength',
      });
    }

    if (schema.pattern) {
      const regex = new RegExp(schema.pattern);
      if (!regex.test(value)) {
        errors.push({
          path,
          message: `String does not match pattern: ${schema.pattern}`,
          value,
          constraint: 'pattern',
        });
      }
    }

    if (schema.format) {
      const validator = this.formatValidators[schema.format];
      if (validator && !validator(value)) {
        errors.push({
          path,
          message: `String does not match format: ${schema.format}`,
          value,
          constraint: 'format',
        });
      }
    }
  }

  /**
   * Validates number values
   */
  private static validateNumber(
    value: number,
    schema: Schema,
    path: string,
    errors: ValidationError[]
  ): void {
    if (schema.minimum !== undefined && value < schema.minimum) {
      errors.push({
        path,
        message: `Number must be at least ${schema.minimum}`,
        value,
        constraint: 'minimum',
      });
    }

    if (schema.maximum !== undefined && value > schema.maximum) {
      errors.push({
        path,
        message: `Number must be at most ${schema.maximum}`,
        value,
        constraint: 'maximum',
      });
    }
  }

  /**
   * Validates object values
   */
  private static validateObject(
    value: Record<string, unknown>,
    schema: Schema,
    path: string,
    errors: ValidationError[]
  ): void {
    // Check required properties
    if (schema.required) {
      for (const requiredProp of schema.required) {
        if (!(requiredProp in value)) {
          errors.push({
            path: this.joinPath(path, requiredProp),
            message: 'Required property is missing',
            constraint: 'required',
          });
        }
      }
    }

    // Validate properties
    if (schema.properties) {
      for (const [propName, propSchema] of Object.entries(schema.properties)) {
        if (propName in value) {
          this.validateValue(
            value[propName],
            propSchema,
            this.joinPath(path, propName),
            errors
          );
        }
      }
    }

    // Check additional properties
    if (schema.additionalProperties === false) {
      const allowedProps = new Set(Object.keys(schema.properties || {}));
      for (const propName of Object.keys(value)) {
        if (!allowedProps.has(propName)) {
          errors.push({
            path: this.joinPath(path, propName),
            message: 'Additional property is not allowed',
            constraint: 'additionalProperties',
          });
        }
      }
    } else if (typeof schema.additionalProperties === 'object') {
      const allowedProps = new Set(Object.keys(schema.properties || {}));
      for (const [propName, propValue] of Object.entries(value)) {
        if (!allowedProps.has(propName)) {
          this.validateValue(
            propValue,
            schema.additionalProperties,
            this.joinPath(path, propName),
            errors
          );
        }
      }
    }
  }

  /**
   * Validates array values
   */
  private static validateArray(
    value: unknown[],
    schema: Schema,
    path: string,
    errors: ValidationError[]
  ): void {
    if (schema.minLength !== undefined && value.length < schema.minLength) {
      errors.push({
        path,
        message: `Array must have at least ${schema.minLength} items`,
        value,
        constraint: 'minLength',
      });
    }

    if (schema.maxLength !== undefined && value.length > schema.maxLength) {
      errors.push({
        path,
        message: `Array must have at most ${schema.maxLength} items`,
        value,
        constraint: 'maxLength',
      });
    }

    if (schema.items) {
      value.forEach((item, index) => {
        this.validateValue(
          item,
          schema.items!,
          this.joinPath(path, index.toString()),
          errors
        );
      });
    }
  }

  /**
   * Validates oneOf constraint
   */
  private static validateOneOf(
    value: unknown,
    schemas: Schema[],
    path: string,
    errors: ValidationError[]
  ): void {
    const validSchemas = schemas.filter(schema => {
      const result = this.validate(value, schema);
      return result.valid;
    });

    if (validSchemas.length !== 1) {
      errors.push({
        path,
        message: `Value must match exactly one of the provided schemas (matched ${validSchemas.length})`,
        value,
        constraint: 'oneOf',
      });
    }
  }

  /**
   * Validates anyOf constraint
   */
  private static validateAnyOf(
    value: unknown,
    schemas: Schema[],
    path: string,
    errors: ValidationError[]
  ): void {
    const validSchemas = schemas.filter(schema => {
      const result = this.validate(value, schema);
      return result.valid;
    });

    if (validSchemas.length === 0) {
      errors.push({
        path,
        message: 'Value must match at least one of the provided schemas',
        value,
        constraint: 'anyOf',
      });
    }
  }

  /**
   * Validates allOf constraint
   */
  private static validateAllOf(
    value: unknown,
    schemas: Schema[],
    path: string,
    errors: ValidationError[]
  ): void {
    for (const schema of schemas) {
      this.validateValue(value, schema, path, errors);
    }
  }

  /**
   * Validates not constraint
   */
  private static validateNot(
    value: unknown,
    schema: Schema,
    path: string,
    errors: ValidationError[]
  ): void {
    const result = this.validate(value, schema);
    if (result.valid) {
      errors.push({
        path,
        message: 'Value must not match the provided schema',
        value,
        constraint: 'not',
      });
    }
  }

  /**
   * Joins path segments
   */
  private static joinPath(basePath: string, segment: string): string {
    if (!basePath) return segment;
    return `${basePath}.${segment}`;
  }
}
