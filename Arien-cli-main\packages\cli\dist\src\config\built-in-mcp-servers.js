/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { MCPServerConfig } from '@arien/arien-cli-core';
/**
 * Built-in MCP servers that are automatically available without manual configuration.
 * These servers have the lowest priority and can be overridden by extensions or user settings.
 */
export const BUILT_IN_MCP_SERVERS = {
    'Context 7': new MCPServerConfig('npx', ['-y', '@upstash/context7-mcp@latest'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'Context7 MCP server for enhanced context management'),
    'Playwright': new MCPServerConfig('npx', ['-y', '@playwright/mcp@latest'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'Playwright MCP server for browser automation'),
    'Sequential thinking': new MCPServerConfig('npx', ['-y', '@modelcontextprotocol/server-sequential-thinking'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'Sequential thinking MCP server for structured reasoning'),
    'Filesystem': new MCPServerConfig('npx', ['-y', '@modelcontextprotocol/server-filesystem', '.'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'Filesystem MCP server for file operations'),
    'Fetch': new MCPServerConfig('npx', ['-y', '@modelcontextprotocol/server-fetch'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'Fetch MCP server for HTTP requests'),
    'Memory': new MCPServerConfig('npx', ['-y', '@modelcontextprotocol/server-memory'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'Memory MCP server for persistent storage'),
    'Time': new MCPServerConfig('npx', ['-y', '@modelcontextprotocol/server-time'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'Time MCP server for date and time operations'),
    'Git': new MCPServerConfig('uvx', ['mcp-server-git', '--repository', '.'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'Git MCP server for version control operations'),
    'Calculator': new MCPServerConfig('uvx', ['mcp-server-calculator'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'Calculator MCP server for mathematical operations'),
    'QR Code': new MCPServerConfig('uvx', ['qr-code-mcp-server'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'QR Code MCP server for QR code generation'),
    'DuckDuckGo Search': new MCPServerConfig('uvx', ['duckduckgo-mcp-server'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'DuckDuckGo Search MCP server for web search'),
    'Docker': new MCPServerConfig('uvx', ['docker-mcp-server'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'Docker MCP server for container management'),
};
/**
 * MCP servers that require API keys or special configuration.
 * These are not included by default but can be easily enabled by users.
 */
export const CONFIGURABLE_MCP_SERVERS = {
    'Brave Search': new MCPServerConfig('npx', ['-y', '@modelcontextprotocol/server-brave-search'], { BRAVE_API_KEY: 'your_brave_api_key_here' }, undefined, undefined, undefined, undefined, undefined, undefined, 'Brave Search MCP server (requires BRAVE_API_KEY)'),
    'Web Scraping AI': new MCPServerConfig('npx', ['-y', 'webscraping-ai-mcp-server'], { WEBSCRAPING_AI_API_KEY: 'your_webscraping_ai_key_here' }, undefined, undefined, undefined, undefined, undefined, undefined, 'Web Scraping AI MCP server (requires WEBSCRAPING_AI_API_KEY)'),
    'PostgreSQL': new MCPServerConfig('npx', ['-y', '@modelcontextprotocol/server-postgres'], { POSTGRES_CONNECTION_STRING: 'postgresql://user:password@localhost:5432/dbname' }, undefined, undefined, undefined, undefined, undefined, undefined, 'PostgreSQL MCP server (requires POSTGRES_CONNECTION_STRING)'),
    'SQLite': new MCPServerConfig('npx', ['-y', '@modelcontextprotocol/server-sqlite', 'path/to/database.db'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'SQLite MCP server (requires database path configuration)'),
    'MongoDB': new MCPServerConfig('uvx', ['mongodb-mcp-server'], { MONGODB_URI: 'mongodb://localhost:27017' }, undefined, undefined, undefined, undefined, undefined, undefined, 'MongoDB MCP server (requires MONGODB_URI)'),
    'GitHub': new MCPServerConfig('npx', ['-y', '@modelcontextprotocol/server-github'], { GITHUB_PERSONAL_ACCESS_TOKEN: 'your_github_token_here' }, undefined, undefined, undefined, undefined, undefined, undefined, 'GitHub MCP server (requires GITHUB_PERSONAL_ACCESS_TOKEN)'),
    'GitLab': new MCPServerConfig('npx', ['-y', '@modelcontextprotocol/server-gitlab'], {
        GITLAB_PERSONAL_ACCESS_TOKEN: 'your_gitlab_token_here',
        GITLAB_API_URL: 'https://gitlab.com/api/v4'
    }, undefined, undefined, undefined, undefined, undefined, undefined, 'GitLab MCP server (requires GITLAB_PERSONAL_ACCESS_TOKEN)'),
    'AWS': new MCPServerConfig('uvx', ['mcp-server-aws'], {
        AWS_ACCESS_KEY_ID: 'your_aws_access_key',
        AWS_SECRET_ACCESS_KEY: 'your_aws_secret_key',
        AWS_DEFAULT_REGION: 'us-east-1'
    }, undefined, undefined, undefined, undefined, undefined, undefined, 'AWS MCP server (requires AWS credentials)'),
    'Google Drive': new MCPServerConfig('uvx', ['gdrive-mcp-server'], {
        GOOGLE_CLIENT_ID: 'your_google_client_id',
        GOOGLE_CLIENT_SECRET: 'your_google_client_secret'
    }, undefined, undefined, undefined, undefined, undefined, undefined, 'Google Drive MCP server (requires Google OAuth credentials)'),
    'Notion': new MCPServerConfig('uvx', ['notion-mcp-server'], { NOTION_API_KEY: 'your_notion_integration_token' }, undefined, undefined, undefined, undefined, undefined, undefined, 'Notion MCP server (requires NOTION_API_KEY)'),
    'Obsidian': new MCPServerConfig('npx', ['-y', 'mcp-obsidian', 'path/to/obsidian/vault'], undefined, undefined, undefined, undefined, undefined, undefined, undefined, 'Obsidian MCP server (requires vault path configuration)'),
    'Linear': new MCPServerConfig('npx', ['-y', 'mcp-linear'], { LINEAR_API_KEY: 'your_linear_api_key' }, undefined, undefined, undefined, undefined, undefined, undefined, 'Linear MCP server (requires LINEAR_API_KEY)'),
    'Slack': new MCPServerConfig('npx', ['-y', '@modelcontextprotocol/server-slack'], { SLACK_BOT_TOKEN: 'your_slack_bot_token' }, undefined, undefined, undefined, undefined, undefined, undefined, 'Slack MCP server (requires SLACK_BOT_TOKEN)'),
    'Discord': new MCPServerConfig('uvx', ['discord-mcp-server'], { DISCORD_BOT_TOKEN: 'your_discord_bot_token' }, undefined, undefined, undefined, undefined, undefined, undefined, 'Discord MCP server (requires DISCORD_BOT_TOKEN)'),
    'OpenAI': new MCPServerConfig('uvx', ['mcp-server-openai'], { OPENAI_API_KEY: 'your_openai_api_key' }, undefined, undefined, undefined, undefined, undefined, undefined, 'OpenAI MCP server (requires OPENAI_API_KEY)'),
    'Perplexity': new MCPServerConfig('uvx', ['mcp-server-perplexity'], { PERPLEXITY_API_KEY: 'your_perplexity_api_key' }, undefined, undefined, undefined, undefined, undefined, undefined, 'Perplexity MCP server (requires PERPLEXITY_API_KEY)'),
    'VirusTotal': new MCPServerConfig('npx', ['-y', 'mcp-virustotal'], { VIRUSTOTAL_API_KEY: 'your_virustotal_api_key' }, undefined, undefined, undefined, undefined, undefined, undefined, 'VirusTotal MCP server (requires VIRUSTOTAL_API_KEY)'),
    'Shodan': new MCPServerConfig('npx', ['-y', 'mcp-shodan'], { SHODAN_API_KEY: 'your_shodan_api_key' }, undefined, undefined, undefined, undefined, undefined, undefined, 'Shodan MCP server (requires SHODAN_API_KEY)'),
    'Kubernetes': new MCPServerConfig('uvx', ['kubernetes-mcp-server'], { KUBECONFIG: 'path/to/kubeconfig' }, undefined, undefined, undefined, undefined, undefined, undefined, 'Kubernetes MCP server (requires KUBECONFIG)'),
    'Weather': new MCPServerConfig('uvx', ['weather-mcp-server'], { WEATHER_API_KEY: 'your_weather_api_key' }, undefined, undefined, undefined, undefined, undefined, undefined, 'Weather MCP server (requires WEATHER_API_KEY)'),
};
/**
 * Get the built-in MCP servers that should be automatically loaded.
 * This function can be extended in the future to include conditional logic
 * for enabling/disabling certain servers based on environment or user preferences.
 */
export function getBuiltInMcpServers() {
    return { ...BUILT_IN_MCP_SERVERS };
}
/**
 * Get configurable MCP servers that require additional setup.
 * These are provided for reference but not automatically loaded.
 */
export function getConfigurableMcpServers() {
    return { ...CONFIGURABLE_MCP_SERVERS };
}
//# sourceMappingURL=built-in-mcp-servers.js.map