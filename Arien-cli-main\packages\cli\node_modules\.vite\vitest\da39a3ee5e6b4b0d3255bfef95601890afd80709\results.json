{"version": "3.2.4", "results": [[":src/config/built-in-mcp-servers.test.ts", {"duration": 24.066400000000613, "failed": false}], [":src/config/config.test.ts", {"duration": 628.7890000000007, "failed": false}], [":src/config/mcp-integration.test.ts", {"duration": 229.85519999999997, "failed": false}], [":src/ui/hooks/slashCommandProcessor.test.ts", {"duration": 311.0689999999995, "failed": false}], [":src/ui/components/shared/text-buffer.test.ts", {"duration": 671.1046999999999, "failed": false}], [":src/ui/hooks/useArienStream.test.tsx", {"duration": 510.6202999999987, "failed": false}], [":src/ui/hooks/useToolScheduler.test.ts", {"duration": 343.2340000000004, "failed": false}], [":src/ui/hooks/atCommandProcessor.test.ts", {"duration": 104.8088000000007, "failed": true}], [":src/config/settings.test.ts", {"duration": 122.98090000000047, "failed": false}], [":src/ui/App.test.tsx", {"duration": 1057.7728000000006, "failed": true}], [":src/ui/hooks/useCompletion.integration.test.ts", {"duration": 1678.3924000000006, "failed": true}], [":src/ui/components/messages/DiffRenderer.test.tsx", {"duration": 612.4161000000004, "failed": false}], [":src/ui/hooks/useAutoAcceptIndicator.test.ts", {"duration": 157.54000000000087, "failed": false}], [":src/ui/components/shared/MaxSizedBox.test.tsx", {"duration": 245.15540000000055, "failed": false}], [":src/nonInteractiveCli.test.ts", {"duration": 36.92020000000048, "failed": false}], [":src/ui/components/messages/ToolMessage.test.tsx", {"duration": 251.72659999999996, "failed": false}], [":src/ui/hooks/useHistoryManager.test.ts", {"duration": 113.3078000000005, "failed": true}], [":src/ui/hooks/useEditorSettings.test.ts", {"duration": 128.0648999999994, "failed": false}], [":src/ui/contexts/SessionContext.test.tsx", {"duration": 107.89459999999963, "failed": false}], [":src/ui/hooks/useInputHistory.test.ts", {"duration": 298.22209999999995, "failed": false}], [":src/ui/hooks/useGitBranchName.test.ts", {"duration": 140.50079999999934, "failed": false}], [":src/config/config.integration.test.ts", {"duration": 175.89560000000074, "failed": false}], [":src/ui/hooks/useShellHistory.test.ts", {"duration": 953.5611000000008, "failed": false}], [":src/ui/components/LoadingIndicator.test.tsx", {"duration": 170.04569999999967, "failed": false}], [":src/ui/components/InputPrompt.test.tsx", {"duration": 843.7648999999992, "failed": false}], [":src/ui/hooks/useConsoleMessages.test.ts", {"duration": 82.38270000000011, "failed": false}], [":src/ui/hooks/usePhraseCycler.test.ts", {"duration": 89.63749999999982, "failed": false}], [":src/ui/hooks/shellCommandProcessor.test.ts", {"duration": 87.5555000000004, "failed": false}], [":src/ui/utils/errorParsing.test.ts", {"duration": 32.69909999999982, "failed": false}], [":src/ui/hooks/useLoadingIndicator.test.ts", {"duration": 111.07409999999982, "failed": false}], [":src/arien.test.tsx", {"duration": 8.847799999999552, "failed": false}], [":src/ui/hooks/useTimer.test.ts", {"duration": 96.30400000000009, "failed": false}], [":src/config/extension.test.ts", {"duration": 54.98900000000003, "failed": false}], [":src/ui/components/AuthDialog.test.tsx", {"duration": 291.40179999999964, "failed": false}], [":src/ui/components/messages/ArienMessage.test.tsx", {"duration": 177.98369999999977, "failed": true}], [":src/ui/components/HistoryItemDisplay.test.tsx", {"duration": 203.57790000000023, "failed": true}], [":src/utils/startupWarnings.test.ts", {"duration": 0, "failed": false}], [":src/ui/components/Stats.test.tsx", {"duration": 133.90450000000055, "failed": false}], [":src/ui/utils/formatters.test.ts", {"duration": 12.707499999999982, "failed": false}], [":src/ui/utils/markdownUtilities.test.ts", {"duration": 10.759800000000268, "failed": false}], [":src/ui/components/StatsDisplay.test.tsx", {"duration": 67.19669999999996, "failed": false}], [":src/ui/components/messages/ToolConfirmationMessage.test.tsx", {"duration": 41.57999999999993, "failed": false}], [":src/ui/components/SessionSummaryDisplay.test.tsx", {"duration": 70.24020000000019, "failed": false}], [":src/ui/utils/textUtils.test.ts", {"duration": 8.288900000000012, "failed": false}]]}