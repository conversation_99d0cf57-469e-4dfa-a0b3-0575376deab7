{"version": 3, "file": "built-in-mcp-servers.js", "sourceRoot": "", "sources": ["../../../src/config/built-in-mcp-servers.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AAExD;;;GAGG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAoC;IACnE,WAAW,EAAE,IAAI,eAAe,CAC9B,KAAK,EACL,CAAC,IAAI,EAAE,8BAA8B,CAAC,EACtC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,qDAAqD,CACtD;IAED,YAAY,EAAE,IAAI,eAAe,CAC/B,KAAK,EACL,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,8CAA8C,CAC/C;IAED,qBAAqB,EAAE,IAAI,eAAe,CACxC,KAAK,EACL,CAAC,IAAI,EAAE,kDAAkD,CAAC,EAC1D,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,yDAAyD,CAC1D;IAED,YAAY,EAAE,IAAI,eAAe,CAC/B,KAAK,EACL,CAAC,IAAI,EAAE,yCAAyC,EAAE,GAAG,CAAC,EACtD,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,2CAA2C,CAC5C;IAED,OAAO,EAAE,IAAI,eAAe,CAC1B,KAAK,EACL,CAAC,IAAI,EAAE,oCAAoC,CAAC,EAC5C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,oCAAoC,CACrC;IAED,QAAQ,EAAE,IAAI,eAAe,CAC3B,KAAK,EACL,CAAC,IAAI,EAAE,qCAAqC,CAAC,EAC7C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,0CAA0C,CAC3C;IAED,MAAM,EAAE,IAAI,eAAe,CACzB,KAAK,EACL,CAAC,IAAI,EAAE,mCAAmC,CAAC,EAC3C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,8CAA8C,CAC/C;IAED,KAAK,EAAE,IAAI,eAAe,CACxB,KAAK,EACL,CAAC,gBAAgB,EAAE,cAAc,EAAE,GAAG,CAAC,EACvC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,+CAA+C,CAChD;IAED,YAAY,EAAE,IAAI,eAAe,CAC/B,KAAK,EACL,CAAC,uBAAuB,CAAC,EACzB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,mDAAmD,CACpD;IAED,SAAS,EAAE,IAAI,eAAe,CAC5B,KAAK,EACL,CAAC,oBAAoB,CAAC,EACtB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,2CAA2C,CAC5C;IAED,mBAAmB,EAAE,IAAI,eAAe,CACtC,KAAK,EACL,CAAC,uBAAuB,CAAC,EACzB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,6CAA6C,CAC9C;IAED,QAAQ,EAAE,IAAI,eAAe,CAC3B,KAAK,EACL,CAAC,mBAAmB,CAAC,EACrB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,4CAA4C,CAC7C;CACF,CAAC;AAEF;;;GAGG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAoC;IACvE,cAAc,EAAE,IAAI,eAAe,CACjC,KAAK,EACL,CAAC,IAAI,EAAE,2CAA2C,CAAC,EACnD,EAAE,aAAa,EAAE,yBAAyB,EAAE,EAC5C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,kDAAkD,CACnD;IAED,iBAAiB,EAAE,IAAI,eAAe,CACpC,KAAK,EACL,CAAC,IAAI,EAAE,2BAA2B,CAAC,EACnC,EAAE,sBAAsB,EAAE,8BAA8B,EAAE,EAC1D,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,8DAA8D,CAC/D;IAED,YAAY,EAAE,IAAI,eAAe,CAC/B,KAAK,EACL,CAAC,IAAI,EAAE,uCAAuC,CAAC,EAC/C,EAAE,0BAA0B,EAAE,kDAAkD,EAAE,EAClF,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,6DAA6D,CAC9D;IAED,QAAQ,EAAE,IAAI,eAAe,CAC3B,KAAK,EACL,CAAC,IAAI,EAAE,qCAAqC,EAAE,qBAAqB,CAAC,EACpE,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,0DAA0D,CAC3D;IAED,SAAS,EAAE,IAAI,eAAe,CAC5B,KAAK,EACL,CAAC,oBAAoB,CAAC,EACtB,EAAE,WAAW,EAAE,2BAA2B,EAAE,EAC5C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,2CAA2C,CAC5C;IAED,QAAQ,EAAE,IAAI,eAAe,CAC3B,KAAK,EACL,CAAC,IAAI,EAAE,qCAAqC,CAAC,EAC7C,EAAE,4BAA4B,EAAE,wBAAwB,EAAE,EAC1D,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,2DAA2D,CAC5D;IAED,QAAQ,EAAE,IAAI,eAAe,CAC3B,KAAK,EACL,CAAC,IAAI,EAAE,qCAAqC,CAAC,EAC7C;QACE,4BAA4B,EAAE,wBAAwB;QACtD,cAAc,EAAE,2BAA2B;KAC5C,EACD,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,2DAA2D,CAC5D;IAED,KAAK,EAAE,IAAI,eAAe,CACxB,KAAK,EACL,CAAC,gBAAgB,CAAC,EAClB;QACE,iBAAiB,EAAE,qBAAqB;QACxC,qBAAqB,EAAE,qBAAqB;QAC5C,kBAAkB,EAAE,WAAW;KAChC,EACD,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,2CAA2C,CAC5C;IAED,cAAc,EAAE,IAAI,eAAe,CACjC,KAAK,EACL,CAAC,mBAAmB,CAAC,EACrB;QACE,gBAAgB,EAAE,uBAAuB;QACzC,oBAAoB,EAAE,2BAA2B;KAClD,EACD,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,6DAA6D,CAC9D;IAED,QAAQ,EAAE,IAAI,eAAe,CAC3B,KAAK,EACL,CAAC,mBAAmB,CAAC,EACrB,EAAE,cAAc,EAAE,+BAA+B,EAAE,EACnD,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,6CAA6C,CAC9C;IAED,UAAU,EAAE,IAAI,eAAe,CAC7B,KAAK,EACL,CAAC,IAAI,EAAE,cAAc,EAAE,wBAAwB,CAAC,EAChD,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,yDAAyD,CAC1D;IAED,QAAQ,EAAE,IAAI,eAAe,CAC3B,KAAK,EACL,CAAC,IAAI,EAAE,YAAY,CAAC,EACpB,EAAE,cAAc,EAAE,qBAAqB,EAAE,EACzC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,6CAA6C,CAC9C;IAED,OAAO,EAAE,IAAI,eAAe,CAC1B,KAAK,EACL,CAAC,IAAI,EAAE,oCAAoC,CAAC,EAC5C,EAAE,eAAe,EAAE,sBAAsB,EAAE,EAC3C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,6CAA6C,CAC9C;IAED,SAAS,EAAE,IAAI,eAAe,CAC5B,KAAK,EACL,CAAC,oBAAoB,CAAC,EACtB,EAAE,iBAAiB,EAAE,wBAAwB,EAAE,EAC/C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,iDAAiD,CAClD;IAED,QAAQ,EAAE,IAAI,eAAe,CAC3B,KAAK,EACL,CAAC,mBAAmB,CAAC,EACrB,EAAE,cAAc,EAAE,qBAAqB,EAAE,EACzC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,6CAA6C,CAC9C;IAED,YAAY,EAAE,IAAI,eAAe,CAC/B,KAAK,EACL,CAAC,uBAAuB,CAAC,EACzB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,EACjD,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,qDAAqD,CACtD;IAED,YAAY,EAAE,IAAI,eAAe,CAC/B,KAAK,EACL,CAAC,IAAI,EAAE,gBAAgB,CAAC,EACxB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,EACjD,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,qDAAqD,CACtD;IAED,QAAQ,EAAE,IAAI,eAAe,CAC3B,KAAK,EACL,CAAC,IAAI,EAAE,YAAY,CAAC,EACpB,EAAE,cAAc,EAAE,qBAAqB,EAAE,EACzC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,6CAA6C,CAC9C;IAED,YAAY,EAAE,IAAI,eAAe,CAC/B,KAAK,EACL,CAAC,uBAAuB,CAAC,EACzB,EAAE,UAAU,EAAE,oBAAoB,EAAE,EACpC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,6CAA6C,CAC9C;IAED,SAAS,EAAE,IAAI,eAAe,CAC5B,KAAK,EACL,CAAC,oBAAoB,CAAC,EACtB,EAAE,eAAe,EAAE,sBAAsB,EAAE,EAC3C,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,+CAA+C,CAChD;CACF,CAAC;AAEF;;;;GAIG;AACH,MAAM,UAAU,oBAAoB;IAClC,OAAO,EAAE,GAAG,oBAAoB,EAAE,CAAC;AACrC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,yBAAyB;IACvC,OAAO,EAAE,GAAG,wBAAwB,EAAE,CAAC;AACzC,CAAC"}