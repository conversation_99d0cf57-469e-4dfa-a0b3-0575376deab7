# Built-in MCP Servers

Arien AI now includes a comprehensive set of built-in MCP (Model Context Protocol) servers that are automatically available without requiring manual configuration. This eliminates the need for users to manually create a `/.arien/settings.json` file in their project directory root for basic MCP functionality.

## Overview

The built-in MCP servers provide essential tools and capabilities that are commonly needed when working with AI assistants. These servers are automatically loaded and configured when you start Arien AI, giving you immediate access to a rich set of tools without any setup.

## Built-in MCP Servers

The following MCP servers are automatically available:

### Core Functionality
- **Context 7**: Enhanced context management for better AI interactions
- **Sequential thinking**: Structured reasoning and problem-solving capabilities
- **Memory**: Persistent storage and recall functionality
- **Fetch**: HTTP request capabilities for web interactions
- **Time**: Date and time operations

### Development Tools
- **Filesystem**: File operations and management
- **Git**: Version control operations
- **Playwright**: Browser automation and testing
- **Docker**: Container management and operations

### Utilities
- **Calculator**: Mathematical operations and calculations
- **QR Code**: QR code generation and processing
- **DuckDuckGo Search**: Web search capabilities (no API key required)

## Configuration

### Automatic Loading

By default, all built-in MCP servers are automatically loaded when Arien AI starts. No configuration is required.

### Disabling Built-in Servers

If you prefer to use only your own MCP server configurations, you can disable the built-in servers by adding the following to your settings:

```json
{
  "enableBuiltInMcpServers": false
}
```

### Overriding Built-in Servers

You can override any built-in MCP server configuration by defining a server with the same name in your settings. The priority order is:

1. **User settings** (highest priority) - Your `/.arien/settings.json` or `~/.arien/settings.json`
2. **Extension settings** (medium priority) - MCP servers from installed extensions
3. **Built-in servers** (lowest priority) - Automatically provided servers

#### Example: Overriding the Git Server

```json
{
  "mcpServers": {
    "Git": {
      "command": "my-custom-git-server",
      "args": ["--custom-flag"],
      "cwd": "/path/to/my/repo"
    }
  }
}
```

## Additional Configurable Servers

Beyond the built-in servers, Arien AI also provides configurations for additional MCP servers that require API keys or special setup. These are not automatically loaded but can be easily enabled by adding the appropriate configuration and environment variables:

### Search and Web Services
- **Brave Search** (requires `BRAVE_API_KEY`)
- **Web Scraping AI** (requires `WEBSCRAPING_AI_API_KEY`)

### Databases
- **PostgreSQL** (requires `POSTGRES_CONNECTION_STRING`)
- **SQLite** (requires database path)
- **MongoDB** (requires `MONGODB_URI`)

### Development Platforms
- **GitHub** (requires `GITHUB_PERSONAL_ACCESS_TOKEN`)
- **GitLab** (requires `GITLAB_PERSONAL_ACCESS_TOKEN`)

### Cloud Services
- **AWS** (requires AWS credentials)
- **Google Drive** (requires Google OAuth credentials)

### Productivity Tools
- **Notion** (requires `NOTION_API_KEY`)
- **Obsidian** (requires vault path)
- **Linear** (requires `LINEAR_API_KEY`)

### Communication
- **Slack** (requires `SLACK_BOT_TOKEN`)
- **Discord** (requires `DISCORD_BOT_TOKEN`)

### AI Services
- **OpenAI** (requires `OPENAI_API_KEY`)
- **Perplexity** (requires `PERPLEXITY_API_KEY`)

### Security and Infrastructure
- **VirusTotal** (requires `VIRUSTOTAL_API_KEY`)
- **Shodan** (requires `SHODAN_API_KEY`)
- **Kubernetes** (requires `KUBECONFIG`)

### Other Services
- **Weather** (requires `WEATHER_API_KEY`)

## Migration from Manual Configuration

If you previously had a manual `/.arien/settings.json` file with MCP server configurations, your existing setup will continue to work without any changes. The built-in servers will be added alongside your existing configurations, and your manual configurations will take precedence over any built-in servers with the same name.

## Benefits

### For New Users
- **Zero Configuration**: Start using MCP tools immediately without setup
- **Rich Functionality**: Access to essential tools out of the box
- **Learning**: Discover available MCP capabilities through built-in examples

### For Existing Users
- **Backward Compatibility**: Existing configurations continue to work
- **Enhanced Capabilities**: Access to additional tools without manual setup
- **Flexibility**: Easy to override or disable built-in servers as needed

## Technical Implementation

The built-in MCP servers are implemented with the following features:

- **Priority System**: User settings > Extension settings > Built-in servers
- **Error Handling**: Robust error handling with fallback mechanisms
- **Validation**: Configuration validation to ensure server compatibility
- **Logging**: Detailed logging for debugging and monitoring
- **Performance**: Efficient loading and minimal overhead

## Troubleshooting

### Disabling Specific Built-in Servers

To disable a specific built-in server without disabling all of them, override it with an empty or invalid configuration:

```json
{
  "mcpServers": {
    "Calculator": null
  }
}
```

### Debugging MCP Server Issues

Enable debug mode to see detailed information about MCP server loading:

```bash
arien --debug
```

This will show which servers are loaded, any conflicts, and error messages.

### Common Issues

1. **Server Conflicts**: If you have naming conflicts, the user configuration will take precedence
2. **Missing Dependencies**: Some servers require `npx` or `uvx` to be available in your PATH
3. **Network Issues**: Some servers may require internet connectivity for package downloads

## Future Enhancements

The built-in MCP server system is designed to be extensible. Future enhancements may include:

- **Conditional Loading**: Load servers based on project type or environment
- **User Preferences**: Fine-grained control over which built-in servers to load
- **Auto-Updates**: Automatic updates to built-in server configurations
- **Community Servers**: Integration with community-contributed MCP servers
