# Arien AI CLI Prompt Guide

Welcome to <PERSON><PERSON>, your interactive CLI agent for software engineering tasks. I am designed to assist you with a wide range of development activities, from understanding code to implementing complex features.

## My Core Capabilities

I can help you with:

*   **Code Understanding:** I can read, analyze, and explain code snippets, files, or entire directories.
*   **Bug Fixing:** I can identify and propose solutions for bugs in your codebase.
*   **Feature Development:** I can assist in implementing new features, from scaffolding to integration.
*   **Code Refactoring:** I can refactor existing code to improve its structure, readability, and maintainability.
*   **Testing:** I can help write, run, and debug tests for your applications.
*   **Configuration Management:** I can read, modify, and create configuration files.
*   **File System Operations:** I can list directories, read/write files, and search for content within your project.
*   **Shell Command Execution:** I can execute shell commands to perform various development tasks, such as running builds, installing dependencies, or starting servers.
*   **Web Interaction:** I can fetch content from URLs and perform web searches to gather information.
*   **Project Setup & Scaffolding:** I can help set up new projects or add new components to existing ones.

## How to Interact with Me

To get the most out of our interaction, please keep the following in mind:

1.  **Be Clear and Specific:** Clearly state your objective. The more precise your request, the better I can assist you.
2.  **Provide Context:** When asking me to modify or analyze code, provide relevant context such as file paths, specific functions, or the overall goal.
3.  **Ask for Clarification:** If you are unsure about my capabilities or how to phrase a request, feel free to ask for clarification.
4.  **Iterative Approach:** For complex tasks, we can work iteratively. Break down large problems into smaller, manageable steps.
5.  **Review My Actions:** I will often propose actions or show you the commands I intend to run. Please review them carefully before approving.
6.  **Feedback is Welcome:** Your feedback helps me improve. If you encounter any issues or have suggestions, please let me know.

## Safety and Best Practices

*   I prioritize the safety and integrity of your codebase. I will always try to adhere to existing project conventions and best practices.
*   Before executing commands that modify your file system or codebase, I will explain their purpose and potential impact.
*   I will never expose sensitive information or introduce insecure code.

## Built-in Tools

I come equipped with a comprehensive set of built-in tools that are automatically available:

*   **File Operations**: Read, write, and manage files and directories
*   **Web Search**: Search the web using DuckDuckGo (no API key required)
*   **Browser Automation**: Automate web browsers using Playwright
*   **Calculations**: Perform mathematical operations and calculations
*   **Time Operations**: Handle date and time-related tasks
*   **Memory Management**: Store and recall information across sessions
*   **Git Operations**: Work with version control systems
*   **QR Code Generation**: Create QR codes for various purposes
*   **Docker Management**: Manage containers and Docker operations
*   **Context Management**: Enhanced context handling for better interactions

These tools are powered by built-in MCP (Model Context Protocol) servers and require no additional configuration.

## Getting Help

*   Type `/help` to see a list of available commands and general assistance.
*   Type `/mcp` to see all available tools and their status.
*   To report a bug or provide feedback, use the `/bug` command.

I am here to help you be more productive. Let's build something great together!