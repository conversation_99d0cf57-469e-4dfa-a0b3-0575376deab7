/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { loadCliConfig } from './config.js';
import { Settings } from './settings.js';
import { Extension } from './extension.js';
import * as os from 'os';

// Mock os.homedir
vi.mock('os', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    homedir: vi.fn(),
  };
});

// Mock the core module
vi.mock('@arien/arien-cli-core', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    loadServerHierarchicalMemory: vi.fn(() =>
      Promise.resolve({
        memoryContent: '',
        fileCount: 0,
      }),
    ),
  };
});

describe('MCP Integration Tests', () => {
  const originalArgv = process.argv;
  const originalEnv = { ...process.env };

  beforeEach(() => {
    vi.resetAllMocks();
    vi.mocked(os.homedir).mockReturnValue('/mock/home/<USER>');
    process.env.ARIEN_API_KEY = 'test-api-key';
    process.argv = ['node', 'script.js'];
  });

  afterEach(() => {
    process.argv = originalArgv;
    process.env = originalEnv;
    vi.restoreAllMocks();
  });

  describe('Built-in MCP Server Integration', () => {
    it('should automatically load built-in MCP servers without any configuration', async () => {
      const settings: Settings = {};
      const extensions: Extension[] = [];
      
      const config = await loadCliConfig(settings, extensions, 'test-session');
      const mcpServers = config.getMcpServers();
      
      // Verify that built-in servers are present
      expect(mcpServers).toBeDefined();
      expect(Object.keys(mcpServers!).length).toBeGreaterThan(0);
      
      // Check for specific built-in servers
      expect(mcpServers).toHaveProperty('Context 7');
      expect(mcpServers).toHaveProperty('Playwright');
      expect(mcpServers).toHaveProperty('Filesystem');
      expect(mcpServers).toHaveProperty('Git');
      expect(mcpServers).toHaveProperty('Calculator');
      expect(mcpServers).toHaveProperty('Memory');
      expect(mcpServers).toHaveProperty('Time');
      expect(mcpServers).toHaveProperty('Fetch');
      expect(mcpServers).toHaveProperty('Sequential thinking');
      expect(mcpServers).toHaveProperty('QR Code');
      expect(mcpServers).toHaveProperty('DuckDuckGo Search');
      expect(mcpServers).toHaveProperty('Docker');
    });

    it('should respect the enableBuiltInMcpServers setting when disabled', async () => {
      const settings: Settings = {
        enableBuiltInMcpServers: false,
      };
      const extensions: Extension[] = [];
      
      const config = await loadCliConfig(settings, extensions, 'test-session');
      const mcpServers = config.getMcpServers();
      
      // Should have no built-in servers
      expect(mcpServers).toEqual({});
    });

    it('should allow user configuration to override built-in servers', async () => {
      const settings: Settings = {
        mcpServers: {
          'Context 7': {
            command: 'custom-context7',
            args: ['--custom-flag'],
          },
          'Custom Server': {
            command: 'my-custom-server',
            args: ['--port', '8080'],
          },
        },
      };
      const extensions: Extension[] = [];
      
      const config = await loadCliConfig(settings, extensions, 'test-session');
      const mcpServers = config.getMcpServers();
      
      // User's Context 7 should override built-in
      expect(mcpServers?.['Context 7']).toMatchObject({
        command: 'custom-context7',
        args: ['--custom-flag'],
      });
      
      // Custom server should be present
      expect(mcpServers?.['Custom Server']).toMatchObject({
        command: 'my-custom-server',
        args: ['--port', '8080'],
      });
      
      // Other built-in servers should still be present
      expect(mcpServers).toHaveProperty('Playwright');
      expect(mcpServers).toHaveProperty('Git');
    });

    it('should allow extension configuration to override built-in servers', async () => {
      const settings: Settings = {};
      const extensions: Extension[] = [
        {
          config: {
            name: 'test-extension',
            version: '1.0.0',
            mcpServers: {
              'Git': {
                command: 'extension-git',
                args: ['--extension-mode'],
              },
              'Extension Server': {
                command: 'extension-server',
                args: ['--ext-arg'],
              },
            },
          },
          contextFiles: [],
        },
      ];
      
      const config = await loadCliConfig(settings, extensions, 'test-session');
      const mcpServers = config.getMcpServers();
      
      // Extension's Git should override built-in
      expect(mcpServers?.['Git']).toMatchObject({
        command: 'extension-git',
        args: ['--extension-mode'],
      });
      
      // Extension server should be present
      expect(mcpServers?.['Extension Server']).toMatchObject({
        command: 'extension-server',
        args: ['--ext-arg'],
      });
      
      // Other built-in servers should still be present
      expect(mcpServers).toHaveProperty('Context 7');
      expect(mcpServers).toHaveProperty('Calculator');
    });

    it('should maintain correct priority: user > extension > built-in', async () => {
      const settings: Settings = {
        mcpServers: {
          'Calculator': {
            command: 'user-calculator',
            args: ['--user-mode'],
          },
        },
      };
      const extensions: Extension[] = [
        {
          config: {
            name: 'test-extension',
            version: '1.0.0',
            mcpServers: {
              'Calculator': {
                command: 'extension-calculator',
                args: ['--extension-mode'],
              },
              'Memory': {
                command: 'extension-memory',
                args: ['--ext-memory'],
              },
            },
          },
          contextFiles: [],
        },
      ];
      
      const config = await loadCliConfig(settings, extensions, 'test-session');
      const mcpServers = config.getMcpServers();
      
      // User setting should win for Calculator
      expect(mcpServers?.['Calculator']).toMatchObject({
        command: 'user-calculator',
        args: ['--user-mode'],
      });
      
      // Extension should win for Memory
      expect(mcpServers?.['Memory']).toMatchObject({
        command: 'extension-memory',
        args: ['--ext-memory'],
      });
      
      // Built-in should be used for others
      expect(mcpServers?.['Context 7']).toMatchObject({
        command: 'npx',
        args: ['-y', '@upstash/context7-mcp@latest'],
      });
    });

    it('should handle mixed configuration scenarios gracefully', async () => {
      const settings: Settings = {
        enableBuiltInMcpServers: true,
        mcpServers: {
          'User Server 1': {
            command: 'user-server-1',
          },
          'User Server 2': {
            url: 'http://localhost:8080/sse',
          },
        },
      };
      const extensions: Extension[] = [
        {
          config: {
            name: 'ext1',
            version: '1.0.0',
            mcpServers: {
              'Ext Server 1': {
                command: 'ext-server-1',
              },
            },
          },
          contextFiles: [],
        },
        {
          config: {
            name: 'ext2',
            version: '1.0.0',
            mcpServers: {
              'Ext Server 2': {
                httpUrl: 'http://localhost:9000/stream',
              },
            },
          },
          contextFiles: [],
        },
      ];
      
      const config = await loadCliConfig(settings, extensions, 'test-session');
      const mcpServers = config.getMcpServers();
      
      // Should have all types of servers
      expect(mcpServers).toHaveProperty('User Server 1');
      expect(mcpServers).toHaveProperty('User Server 2');
      expect(mcpServers).toHaveProperty('Ext Server 1');
      expect(mcpServers).toHaveProperty('Ext Server 2');
      
      // Built-in servers should still be present
      expect(mcpServers).toHaveProperty('Context 7');
      expect(mcpServers).toHaveProperty('Git');
      expect(mcpServers).toHaveProperty('Calculator');
      
      // Total should be reasonable (built-in + user + extension)
      const totalServers = Object.keys(mcpServers!).length;
      expect(totalServers).toBeGreaterThan(15); // At least 12 built-in + 4 custom
    });
  });
});
