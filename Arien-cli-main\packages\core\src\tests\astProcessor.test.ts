/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ASTProcessor, CodeSymbol, FileStructure } from '../code_assist/astProcessor.js';
import * as fs from 'fs/promises';
import * as path from 'path';

// Mock fs module
vi.mock('fs/promises');

describe('ASTProcessor', () => {
  let processor: ASTProcessor;
  const mockFs = vi.mocked(fs);

  beforeEach(() => {
    processor = new ASTProcessor();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('analyzeFile', () => {
    it('should analyze a TypeScript file correctly', async () => {
      const mockContent = `
import { Component } from 'react';

export interface User {
  id: number;
  name: string;
}

export class UserService {
  private users: User[] = [];

  public async getUser(id: number): Promise<User | null> {
    return this.users.find(u => u.id === id) || null;
  }

  private validateUser(user: User): boolean {
    return user.id > 0 && user.name.length > 0;
  }
}

export const API_URL = 'https://api.example.com';
`;

      mockFs.readFile.mockResolvedValue(mockContent);

      const result = await processor.analyzeFile('/test/file.ts');

      expect(result).toBeDefined();
      expect(result?.language).toBe('typescript');
      expect(result?.symbols).toHaveLength(5); // User interface, UserService class, getUser method, validateUser method, API_URL constant

      // Check interface
      const userInterface = result?.symbols.find(s => s.name === 'User' && s.type === 'interface');
      expect(userInterface).toBeDefined();
      expect(userInterface?.location.line).toBe(4);

      // Check class
      const userServiceClass = result?.symbols.find(s => s.name === 'UserService' && s.type === 'class');
      expect(userServiceClass).toBeDefined();
      expect(userServiceClass?.location.line).toBe(9);

      // Check public method
      const getUserMethod = result?.symbols.find(s => s.name === 'getUser' && s.type === 'method');
      expect(getUserMethod).toBeDefined();
      expect(getUserMethod?.visibility).toBe('public');

      // Check private method
      const validateUserMethod = result?.symbols.find(s => s.name === 'validateUser' && s.type === 'method');
      expect(validateUserMethod).toBeDefined();
      expect(validateUserMethod?.visibility).toBe('private');

      // Check constant
      const apiUrlConstant = result?.symbols.find(s => s.name === 'API_URL' && s.type === 'constant');
      expect(apiUrlConstant).toBeDefined();

      // Check imports
      expect(result?.imports).toContain("import { Component } from 'react';");

      // Check exports
      expect(result?.exports).toHaveLength(3); // interface, class, constant
    });

    it('should analyze a Python file correctly', async () => {
      const mockContent = `
import os
from typing import List, Optional

class DatabaseManager:
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
    
    def connect(self) -> bool:
        """Connect to the database"""
        return True
    
    def get_users(self) -> List[dict]:
        """Get all users from database"""
        return []

def process_data(data: List[dict]) -> Optional[dict]:
    """Process the input data"""
    if not data:
        return None
    return data[0]
`;

      mockFs.readFile.mockResolvedValue(mockContent);

      const result = await processor.analyzeFile('/test/file.py');

      expect(result).toBeDefined();
      expect(result?.language).toBe('python');
      expect(result?.symbols).toHaveLength(4); // DatabaseManager class, connect method, get_users method, process_data function

      // Check class
      const dbManagerClass = result?.symbols.find(s => s.name === 'DatabaseManager' && s.type === 'class');
      expect(dbManagerClass).toBeDefined();

      // Check function
      const processDataFunction = result?.symbols.find(s => s.name === 'process_data' && s.type === 'function');
      expect(processDataFunction).toBeDefined();

      // Check imports
      expect(result?.imports).toContain('import os');
      expect(result?.imports).toContain('from typing import List, Optional');
    });

    it('should return null for unsupported file types', async () => {
      mockFs.readFile.mockResolvedValue('Some binary content');

      const result = await processor.analyzeFile('/test/file.bin');

      expect(result).toBeNull();
    });

    it('should handle file read errors gracefully', async () => {
      mockFs.readFile.mockRejectedValue(new Error('File not found'));

      const result = await processor.analyzeFile('/test/nonexistent.ts');

      expect(result).toBeNull();
    });
  });

  describe('findSymbolDefinitions', () => {
    it('should find symbol definitions across multiple files', async () => {
      const structures: FileStructure[] = [
        {
          file: '/test/file1.ts',
          language: 'typescript',
          symbols: [
            {
              name: 'UserService',
              type: 'class',
              location: { file: '/test/file1.ts', line: 5, column: 1 },
              scope: 'global',
            },
            {
              name: 'getUser',
              type: 'method',
              location: { file: '/test/file1.ts', line: 10, column: 3 },
              scope: 'class',
            },
          ],
          imports: [],
          exports: [],
          dependencies: [],
        },
        {
          file: '/test/file2.ts',
          language: 'typescript',
          symbols: [
            {
              name: 'UserService',
              type: 'class',
              location: { file: '/test/file2.ts', line: 3, column: 1 },
              scope: 'global',
            },
          ],
          imports: [],
          exports: [],
          dependencies: [],
        },
      ];

      const definitions = await processor.findSymbolDefinitions('UserService', structures);

      expect(definitions).toHaveLength(2);
      expect(definitions[0].location.file).toBe('/test/file1.ts');
      expect(definitions[1].location.file).toBe('/test/file2.ts');
    });

    it('should find partial matches', async () => {
      const structures: FileStructure[] = [
        {
          file: '/test/file1.ts',
          language: 'typescript',
          symbols: [
            {
              name: 'getUserById',
              type: 'function',
              location: { file: '/test/file1.ts', line: 5, column: 1 },
              scope: 'global',
            },
            {
              name: 'getUserByName',
              type: 'function',
              location: { file: '/test/file1.ts', line: 10, column: 1 },
              scope: 'global',
            },
          ],
          imports: [],
          exports: [],
          dependencies: [],
        },
      ];

      const definitions = await processor.findSymbolDefinitions('getUser', structures);

      expect(definitions).toHaveLength(2);
    });
  });

  describe('findSymbolReferences', () => {
    it('should find symbol references in files', async () => {
      const mockContent1 = `
const user = new UserService();
user.getUser(123);
`;
      const mockContent2 = `
import { UserService } from './user-service';
const service = new UserService();
`;

      mockFs.readFile
        .mockResolvedValueOnce(mockContent1)
        .mockResolvedValueOnce(mockContent2);

      const references = await processor.findSymbolReferences('UserService', ['/test/file1.ts', '/test/file2.ts']);

      expect(references).toHaveLength(3);
      expect(references[0].file).toBe('/test/file1.ts');
      expect(references[0].line).toBe(2);
      expect(references[1].file).toBe('/test/file2.ts');
      expect(references[1].line).toBe(2);
      expect(references[2].file).toBe('/test/file2.ts');
      expect(references[2].line).toBe(3);
    });

    it('should handle file read errors gracefully', async () => {
      mockFs.readFile.mockRejectedValue(new Error('File not found'));

      const references = await processor.findSymbolReferences('UserService', ['/test/nonexistent.ts']);

      expect(references).toHaveLength(0);
    });
  });

  describe('analyzeDirectory', () => {
    it('should analyze all files in a directory', async () => {
      const mockDirEntries = [
        { name: 'file1.ts', isFile: () => true, isDirectory: () => false },
        { name: 'file2.js', isFile: () => true, isDirectory: () => false },
        { name: 'subdir', isFile: () => false, isDirectory: () => true },
        { name: 'file3.py', isFile: () => true, isDirectory: () => false },
      ];

      const mockSubDirEntries = [
        { name: 'file4.ts', isFile: () => true, isDirectory: () => false },
      ];

      mockFs.readdir
        .mockResolvedValueOnce(mockDirEntries as any)
        .mockResolvedValueOnce(mockSubDirEntries as any);

      mockFs.readFile.mockResolvedValue('export class TestClass {}');

      const results = await processor.analyzeDirectory('/test');

      expect(mockFs.readdir).toHaveBeenCalledWith('/test', { withFileTypes: true });
      expect(results).toHaveLength(4); // All supported files should be analyzed
    });
  });

  describe('language detection', () => {
    it('should detect TypeScript files', () => {
      expect(processor['detectLanguage']('/test/file.ts')).toBe('typescript');
      expect(processor['detectLanguage']('/test/file.tsx')).toBe('typescript');
    });

    it('should detect JavaScript files', () => {
      expect(processor['detectLanguage']('/test/file.js')).toBe('javascript');
      expect(processor['detectLanguage']('/test/file.jsx')).toBe('javascript');
    });

    it('should detect Python files', () => {
      expect(processor['detectLanguage']('/test/file.py')).toBe('python');
    });

    it('should detect Java files', () => {
      expect(processor['detectLanguage']('/test/file.java')).toBe('java');
    });

    it('should return unknown for unsupported extensions', () => {
      expect(processor['detectLanguage']('/test/file.xyz')).toBe('unknown');
    });
  });

  describe('configuration', () => {
    it('should respect exclude patterns', async () => {
      const processorWithExcludes = new ASTProcessor({
        excludePatterns: ['**/node_modules/**', '**/dist/**'],
      });

      expect(processorWithExcludes['shouldExclude']('/test/node_modules/package/file.js')).toBe(true);
      expect(processorWithExcludes['shouldExclude']('/test/dist/bundle.js')).toBe(true);
      expect(processorWithExcludes['shouldExclude']('/test/src/file.js')).toBe(false);
    });

    it('should respect max depth setting', async () => {
      const processorWithDepth = new ASTProcessor({
        maxDepth: 2,
      });

      const mockDirEntries = [
        { name: 'level1', isFile: () => false, isDirectory: () => true },
      ];

      mockFs.readdir.mockResolvedValue(mockDirEntries as any);

      // This test would need more complex mocking to fully test depth limiting
      // For now, we just verify the configuration is set
      expect(processorWithDepth['config'].maxDepth).toBe(2);
    });
  });
});
