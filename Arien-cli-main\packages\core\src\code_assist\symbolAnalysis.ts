/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { ASTProcessor, CodeSymbol, FileStructure } from './astProcessor.js';
import * as path from 'path';
import * as fs from 'fs/promises';

/**
 * Represents a dependency relationship between symbols
 */
export interface SymbolDependency {
  from: CodeSymbol;
  to: CodeSymbol;
  type: 'calls' | 'extends' | 'implements' | 'imports' | 'references';
  location: {
    file: string;
    line: number;
  };
}

/**
 * Represents a code completion suggestion
 */
export interface CompletionSuggestion {
  label: string;
  kind: 'function' | 'class' | 'interface' | 'variable' | 'method' | 'property';
  detail?: string;
  documentation?: string;
  insertText?: string;
  sortText?: string;
}

/**
 * Represents a code diagnostic (error, warning, info)
 */
export interface CodeDiagnostic {
  file: string;
  line: number;
  column: number;
  severity: 'error' | 'warning' | 'info' | 'hint';
  message: string;
  code?: string;
  source?: string;
}

/**
 * Configuration for symbol analysis
 */
export interface SymbolAnalysisConfig {
  includeExternalDependencies?: boolean;
  analyzeCallGraph?: boolean;
  detectUnusedSymbols?: boolean;
  detectCircularDependencies?: boolean;
  maxAnalysisDepth?: number;
}

/**
 * Advanced symbol analysis service for code understanding
 */
export class SymbolAnalysisService {
  private astProcessor: ASTProcessor;
  private config: SymbolAnalysisConfig;
  private symbolCache: Map<string, FileStructure> = new Map();
  private dependencyGraph: Map<string, SymbolDependency[]> = new Map();

  constructor(config: SymbolAnalysisConfig = {}) {
    this.config = {
      includeExternalDependencies: false,
      analyzeCallGraph: true,
      detectUnusedSymbols: true,
      detectCircularDependencies: true,
      maxAnalysisDepth: 5,
      ...config,
    };
    
    this.astProcessor = new ASTProcessor({
      includePrivate: true,
      includeDocumentation: true,
      maxDepth: this.config.maxAnalysisDepth,
    });
  }

  /**
   * Analyzes a project and builds a comprehensive symbol index
   */
  async analyzeProject(projectPath: string): Promise<{
    structures: FileStructure[];
    dependencies: SymbolDependency[];
    diagnostics: CodeDiagnostic[];
  }> {
    console.log(`Starting project analysis for: ${projectPath}`);
    
    // Analyze all files in the project
    const structures = await this.astProcessor.analyzeDirectory(projectPath);
    
    // Cache the structures for faster lookups
    for (const structure of structures) {
      this.symbolCache.set(structure.file, structure);
    }

    // Build dependency graph
    const dependencies = await this.buildDependencyGraph(structures);
    
    // Run diagnostics
    const diagnostics = await this.runDiagnostics(structures, dependencies);

    console.log(`Analysis complete: ${structures.length} files, ${dependencies.length} dependencies, ${diagnostics.length} diagnostics`);

    return {
      structures,
      dependencies,
      diagnostics,
    };
  }

  /**
   * Provides code completion suggestions for a given position
   */
  async getCompletionSuggestions(
    filePath: string,
    line: number,
    column: number,
    context?: string
  ): Promise<CompletionSuggestion[]> {
    const suggestions: CompletionSuggestion[] = [];
    
    // Get the file structure
    const structure = this.symbolCache.get(filePath) || await this.astProcessor.analyzeFile(filePath);
    if (!structure) return suggestions;

    // Add symbols from current file
    for (const symbol of structure.symbols) {
      suggestions.push({
        label: symbol.name,
        kind: symbol.type as any,
        detail: symbol.signature,
        documentation: symbol.documentation,
        insertText: this.generateInsertText(symbol),
        sortText: this.generateSortText(symbol, filePath),
      });
    }

    // Add symbols from imported files
    for (const importPath of structure.imports) {
      const resolvedPath = this.resolveImportPath(importPath, filePath);
      if (resolvedPath) {
        const importedStructure = this.symbolCache.get(resolvedPath);
        if (importedStructure) {
          for (const symbol of importedStructure.symbols) {
            if (this.isSymbolExported(symbol, importedStructure)) {
              suggestions.push({
                label: symbol.name,
                kind: symbol.type as any,
                detail: `${symbol.signature} (from ${path.basename(resolvedPath)})`,
                documentation: symbol.documentation,
                insertText: symbol.name,
                sortText: `z_${symbol.name}`, // Lower priority for external symbols
              });
            }
          }
        }
      }
    }

    // Add built-in language constructs
    suggestions.push(...this.getBuiltInSuggestions(structure.language));

    return suggestions.sort((a, b) => (a.sortText || a.label).localeCompare(b.sortText || b.label));
  }

  /**
   * Finds all references to a symbol
   */
  async findReferences(symbolName: string, filePath?: string): Promise<Array<{
    file: string;
    line: number;
    column: number;
    context: string;
  }>> {
    const references: Array<{ file: string; line: number; column: number; context: string }> = [];
    
    // Search in all cached files or specific file
    const filesToSearch = filePath ? [filePath] : Array.from(this.symbolCache.keys());
    
    for (const file of filesToSearch) {
      try {
        const content = await fs.readFile(file, 'utf-8');
        const lines = content.split('\n');

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          let columnIndex = 0;
          
          while ((columnIndex = line.indexOf(symbolName, columnIndex)) !== -1) {
            // Check if it's a whole word match
            const before = columnIndex > 0 ? line[columnIndex - 1] : ' ';
            const after = columnIndex + symbolName.length < line.length ? line[columnIndex + symbolName.length] : ' ';
            
            if (!/\w/.test(before) && !/\w/.test(after)) {
              references.push({
                file,
                line: i + 1,
                column: columnIndex + 1,
                context: line.trim(),
              });
            }
            
            columnIndex += symbolName.length;
          }
        }
      } catch (error) {
        console.error(`Error searching file ${file}:`, error);
      }
    }

    return references;
  }

  /**
   * Finds the definition of a symbol
   */
  async findDefinition(symbolName: string, filePath?: string): Promise<CodeSymbol | null> {
    // First check the current file if specified
    if (filePath) {
      const structure = this.symbolCache.get(filePath);
      if (structure) {
        const symbol = structure.symbols.find(s => s.name === symbolName);
        if (symbol) return symbol;
      }
    }

    // Search in all cached files
    for (const structure of this.symbolCache.values()) {
      const symbol = structure.symbols.find(s => s.name === symbolName);
      if (symbol) return symbol;
    }

    return null;
  }

  /**
   * Gets hover information for a symbol at a specific position
   */
  async getHoverInfo(filePath: string, line: number, column: number): Promise<{
    symbol?: CodeSymbol;
    documentation?: string;
    type?: string;
  } | null> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      
      if (line > lines.length) return null;
      
      const currentLine = lines[line - 1];
      const wordMatch = this.getWordAtPosition(currentLine, column);
      
      if (!wordMatch) return null;
      
      const symbol = await this.findDefinition(wordMatch, filePath);
      
      if (symbol) {
        return {
          symbol,
          documentation: symbol.documentation,
          type: symbol.type,
        };
      }
    } catch (error) {
      console.error(`Error getting hover info:`, error);
    }

    return null;
  }

  /**
   * Builds a dependency graph between symbols
   */
  private async buildDependencyGraph(structures: FileStructure[]): Promise<SymbolDependency[]> {
    const dependencies: SymbolDependency[] = [];

    for (const structure of structures) {
      // Analyze imports
      for (const importStatement of structure.imports) {
        const importedPath = this.resolveImportPath(importStatement, structure.file);
        if (importedPath) {
          const importedStructure = structures.find(s => s.file === importedPath);
          if (importedStructure) {
            // Create dependencies for imported symbols
            const importedSymbols = this.extractImportedSymbols(importStatement);
            for (const importedSymbol of importedSymbols) {
              const targetSymbol = importedStructure.symbols.find(s => s.name === importedSymbol);
              if (targetSymbol) {
                dependencies.push({
                  from: { name: structure.file, type: 'variable', location: { file: structure.file, line: 1, column: 1 }, scope: 'global' },
                  to: targetSymbol,
                  type: 'imports',
                  location: { file: structure.file, line: 1 },
                });
              }
            }
          }
        }
      }

      // Analyze function calls and references
      if (this.config.analyzeCallGraph) {
        const callDependencies = await this.analyzeCallGraph(structure);
        dependencies.push(...callDependencies);
      }
    }

    return dependencies;
  }

  /**
   * Analyzes function calls within a file structure
   */
  private async analyzeCallGraph(structure: FileStructure): Promise<SymbolDependency[]> {
    const dependencies: SymbolDependency[] = [];
    
    try {
      const content = await fs.readFile(structure.file, 'utf-8');
      const lines = content.split('\n');

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        // Simple pattern matching for function calls
        const callMatches = line.matchAll(/(\w+)\s*\(/g);
        for (const match of callMatches) {
          const calledFunction = match[1];
          
          // Find the function definition
          const targetSymbol = structure.symbols.find(s => s.name === calledFunction && s.type === 'function');
          if (targetSymbol) {
            dependencies.push({
              from: { name: 'caller', type: 'function', location: { file: structure.file, line: i + 1, column: 1 }, scope: 'local' },
              to: targetSymbol,
              type: 'calls',
              location: { file: structure.file, line: i + 1 },
            });
          }
        }
      }
    } catch (error) {
      console.error(`Error analyzing call graph for ${structure.file}:`, error);
    }

    return dependencies;
  }

  /**
   * Runs various diagnostics on the analyzed code
   */
  private async runDiagnostics(structures: FileStructure[], dependencies: SymbolDependency[]): Promise<CodeDiagnostic[]> {
    const diagnostics: CodeDiagnostic[] = [];

    // Detect unused symbols
    if (this.config.detectUnusedSymbols) {
      diagnostics.push(...this.detectUnusedSymbols(structures, dependencies));
    }

    // Detect circular dependencies
    if (this.config.detectCircularDependencies) {
      diagnostics.push(...this.detectCircularDependencies(dependencies));
    }

    // Detect missing imports
    diagnostics.push(...this.detectMissingImports(structures));

    return diagnostics;
  }

  /**
   * Detects unused symbols in the codebase
   */
  private detectUnusedSymbols(structures: FileStructure[], dependencies: SymbolDependency[]): CodeDiagnostic[] {
    const diagnostics: CodeDiagnostic[] = [];
    const usedSymbols = new Set<string>();

    // Mark symbols that are referenced in dependencies
    for (const dep of dependencies) {
      usedSymbols.add(`${dep.to.location.file}:${dep.to.name}`);
    }

    // Check each symbol
    for (const structure of structures) {
      for (const symbol of structure.symbols) {
        const symbolKey = `${symbol.location.file}:${symbol.name}`;
        
        if (!usedSymbols.has(symbolKey) && symbol.visibility !== 'public') {
          diagnostics.push({
            file: symbol.location.file,
            line: symbol.location.line,
            column: symbol.location.column,
            severity: 'warning',
            message: `Symbol '${symbol.name}' is declared but never used`,
            code: 'unused-symbol',
            source: 'symbol-analysis',
          });
        }
      }
    }

    return diagnostics;
  }

  /**
   * Detects circular dependencies
   */
  private detectCircularDependencies(dependencies: SymbolDependency[]): CodeDiagnostic[] {
    const diagnostics: CodeDiagnostic[] = [];
    const graph = new Map<string, string[]>();

    // Build adjacency list
    for (const dep of dependencies) {
      if (dep.type === 'imports') {
        const fromFile = dep.from.location.file;
        const toFile = dep.to.location.file;
        
        if (!graph.has(fromFile)) {
          graph.set(fromFile, []);
        }
        graph.get(fromFile)!.push(toFile);
      }
    }

    // Detect cycles using DFS
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (node: string, path: string[]): boolean => {
      if (recursionStack.has(node)) {
        // Found a cycle
        const cycleStart = path.indexOf(node);
        const cycle = path.slice(cycleStart).concat(node);
        
        diagnostics.push({
          file: node,
          line: 1,
          column: 1,
          severity: 'error',
          message: `Circular dependency detected: ${cycle.map(f => path.basename(f)).join(' -> ')}`,
          code: 'circular-dependency',
          source: 'symbol-analysis',
        });
        
        return true;
      }

      if (visited.has(node)) return false;

      visited.add(node);
      recursionStack.add(node);

      const neighbors = graph.get(node) || [];
      for (const neighbor of neighbors) {
        if (hasCycle(neighbor, [...path, node])) {
          return true;
        }
      }

      recursionStack.delete(node);
      return false;
    };

    for (const node of graph.keys()) {
      if (!visited.has(node)) {
        hasCycle(node, []);
      }
    }

    return diagnostics;
  }

  /**
   * Detects missing imports
   */
  private detectMissingImports(structures: FileStructure[]): CodeDiagnostic[] {
    const diagnostics: CodeDiagnostic[] = [];

    for (const structure of structures) {
      const importedSymbols = new Set<string>();
      
      // Extract imported symbols
      for (const importStatement of structure.imports) {
        const symbols = this.extractImportedSymbols(importStatement);
        symbols.forEach(s => importedSymbols.add(s));
      }

      // Check if used symbols are imported
      // This is a simplified check - in practice, you'd need more sophisticated analysis
      for (const symbol of structure.symbols) {
        if (symbol.type === 'function' && symbol.signature) {
          // Look for potential external references in the signature
          const externalRefs = symbol.signature.match(/\b[A-Z]\w+\b/g) || [];
          for (const ref of externalRefs) {
            if (!importedSymbols.has(ref) && !this.isBuiltInType(ref, structure.language)) {
              diagnostics.push({
                file: symbol.location.file,
                line: symbol.location.line,
                column: symbol.location.column,
                severity: 'warning',
                message: `'${ref}' is used but not imported`,
                code: 'missing-import',
                source: 'symbol-analysis',
              });
            }
          }
        }
      }
    }

    return diagnostics;
  }

  /**
   * Helper methods
   */
  private resolveImportPath(importStatement: string, currentFile: string): string | null {
    // Extract the import path from the statement
    const match = importStatement.match(/from\s+['"]([^'"]+)['"]|require\(['"]([^'"]+)['"]\)|import\s+['"]([^'"]+)['"]/) ||
                  importStatement.match(/import\s+.*\s+from\s+['"]([^'"]+)['"]/);
    
    if (!match) return null;
    
    const importPath = match[1] || match[2] || match[3];
    if (!importPath) return null;

    // Handle relative imports
    if (importPath.startsWith('.')) {
      const currentDir = path.dirname(currentFile);
      return path.resolve(currentDir, importPath);
    }

    // For absolute imports, you'd need to resolve based on the project structure
    // This is a simplified implementation
    return null;
  }

  private extractImportedSymbols(importStatement: string): string[] {
    const symbols: string[] = [];
    
    // Extract named imports: import { a, b, c } from 'module'
    const namedMatch = importStatement.match(/import\s+\{([^}]+)\}/);
    if (namedMatch) {
      const namedImports = namedMatch[1].split(',').map(s => s.trim().split(' as ')[0]);
      symbols.push(...namedImports);
    }

    // Extract default import: import defaultName from 'module'
    const defaultMatch = importStatement.match(/import\s+(\w+)\s+from/);
    if (defaultMatch) {
      symbols.push(defaultMatch[1]);
    }

    return symbols;
  }

  private isSymbolExported(symbol: CodeSymbol, structure: FileStructure): boolean {
    // Check if the symbol is in the exports list
    return structure.exports.some(exp => exp.includes(symbol.name));
  }

  private getBuiltInSuggestions(language: string): CompletionSuggestion[] {
    const suggestions: CompletionSuggestion[] = [];

    switch (language) {
      case 'typescript':
      case 'javascript':
        suggestions.push(
          { label: 'console', kind: 'variable', detail: 'Console object' },
          { label: 'Promise', kind: 'class', detail: 'Promise constructor' },
          { label: 'Array', kind: 'class', detail: 'Array constructor' },
          { label: 'Object', kind: 'class', detail: 'Object constructor' },
          { label: 'String', kind: 'class', detail: 'String constructor' },
          { label: 'Number', kind: 'class', detail: 'Number constructor' },
          { label: 'Boolean', kind: 'class', detail: 'Boolean constructor' },
        );
        break;
      case 'python':
        suggestions.push(
          { label: 'print', kind: 'function', detail: 'print(*values, sep=" ", end="\\n", file=sys.stdout, flush=False)' },
          { label: 'len', kind: 'function', detail: 'len(obj) -> int' },
          { label: 'str', kind: 'class', detail: 'str(object="") -> str' },
          { label: 'int', kind: 'class', detail: 'int(x=0) -> int' },
          { label: 'list', kind: 'class', detail: 'list() -> new empty list' },
          { label: 'dict', kind: 'class', detail: 'dict() -> new empty dictionary' },
        );
        break;
    }

    return suggestions;
  }

  private generateInsertText(symbol: CodeSymbol): string {
    if (symbol.type === 'function' || symbol.type === 'method') {
      return `${symbol.name}($1)$0`;
    }
    return symbol.name;
  }

  private generateSortText(symbol: CodeSymbol, currentFile: string): string {
    // Prioritize symbols from the current file
    if (symbol.location.file === currentFile) {
      return `a_${symbol.name}`;
    }
    return `b_${symbol.name}`;
  }

  private getWordAtPosition(line: string, column: number): string | null {
    const wordRegex = /\b\w+\b/g;
    let match;
    
    while ((match = wordRegex.exec(line)) !== null) {
      if (column >= match.index && column <= match.index + match[0].length) {
        return match[0];
      }
    }
    
    return null;
  }

  private isBuiltInType(typeName: string, language: string): boolean {
    const builtInTypes: Record<string, string[]> = {
      typescript: ['string', 'number', 'boolean', 'object', 'Array', 'Promise', 'Date', 'RegExp'],
      javascript: ['String', 'Number', 'Boolean', 'Object', 'Array', 'Promise', 'Date', 'RegExp'],
      python: ['str', 'int', 'float', 'bool', 'list', 'dict', 'tuple', 'set'],
      java: ['String', 'Integer', 'Boolean', 'Object', 'List', 'Map', 'Set'],
    };

    return builtInTypes[language]?.includes(typeName) || false;
  }
}
