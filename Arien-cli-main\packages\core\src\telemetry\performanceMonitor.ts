/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { performance } from 'perf_hooks';

/**
 * Represents a performance metric
 */
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  timestamp: number;
  tags?: Record<string, string>;
}

/**
 * Represents a performance span for tracking operations
 */
export interface PerformanceSpan {
  id: string;
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  parentId?: string;
  tags?: Record<string, string>;
  metrics?: PerformanceMetric[];
}

/**
 * Configuration for performance monitoring
 */
export interface PerformanceMonitorConfig {
  enabled: boolean;
  sampleRate: number; // 0.0 to 1.0
  maxSpans: number;
  maxMetrics: number;
  enableMemoryTracking: boolean;
  enableCpuTracking: boolean;
}

/**
 * Performance monitoring service for tracking application performance
 */
export class PerformanceMonitor {
  private config: PerformanceMonitorConfig;
  private spans: Map<string, PerformanceSpan> = new Map();
  private metrics: PerformanceMetric[] = [];
  private spanIdCounter = 0;
  private memoryBaseline?: NodeJS.MemoryUsage;

  constructor(config: Partial<PerformanceMonitorConfig> = {}) {
    this.config = {
      enabled: true,
      sampleRate: 1.0,
      maxSpans: 1000,
      maxMetrics: 5000,
      enableMemoryTracking: true,
      enableCpuTracking: true,
      ...config,
    };

    if (this.config.enableMemoryTracking) {
      this.memoryBaseline = process.memoryUsage();
      this.startMemoryMonitoring();
    }
  }

  /**
   * Starts a new performance span
   */
  startSpan(name: string, parentId?: string, tags?: Record<string, string>): string {
    if (!this.config.enabled || Math.random() > this.config.sampleRate) {
      return '';
    }

    const spanId = `span_${++this.spanIdCounter}_${Date.now()}`;
    const span: PerformanceSpan = {
      id: spanId,
      name,
      startTime: performance.now(),
      parentId,
      tags,
      metrics: [],
    };

    this.spans.set(spanId, span);
    this.cleanupOldSpans();

    return spanId;
  }

  /**
   * Ends a performance span
   */
  endSpan(spanId: string, tags?: Record<string, string>): PerformanceSpan | null {
    if (!spanId || !this.spans.has(spanId)) {
      return null;
    }

    const span = this.spans.get(spanId)!;
    span.endTime = performance.now();
    span.duration = span.endTime - span.startTime;
    
    if (tags) {
      span.tags = { ...span.tags, ...tags };
    }

    // Record the span as a metric
    this.recordMetric({
      name: `span.${span.name}.duration`,
      value: span.duration,
      unit: 'ms',
      timestamp: Date.now(),
      tags: span.tags,
    });

    return span;
  }

  /**
   * Records a custom metric
   */
  recordMetric(metric: PerformanceMetric): void {
    if (!this.config.enabled) return;

    this.metrics.push(metric);
    this.cleanupOldMetrics();
  }

  /**
   * Records memory usage metrics
   */
  recordMemoryUsage(tags?: Record<string, string>): void {
    if (!this.config.enableMemoryTracking) return;

    const memUsage = process.memoryUsage();
    const timestamp = Date.now();

    this.recordMetric({
      name: 'memory.heap.used',
      value: memUsage.heapUsed,
      unit: 'bytes',
      timestamp,
      tags,
    });

    this.recordMetric({
      name: 'memory.heap.total',
      value: memUsage.heapTotal,
      unit: 'bytes',
      timestamp,
      tags,
    });

    this.recordMetric({
      name: 'memory.external',
      value: memUsage.external,
      unit: 'bytes',
      timestamp,
      tags,
    });

    this.recordMetric({
      name: 'memory.rss',
      value: memUsage.rss,
      unit: 'bytes',
      timestamp,
      tags,
    });

    if (this.memoryBaseline) {
      this.recordMetric({
        name: 'memory.heap.growth',
        value: memUsage.heapUsed - this.memoryBaseline.heapUsed,
        unit: 'bytes',
        timestamp,
        tags,
      });
    }
  }

  /**
   * Records CPU usage metrics
   */
  recordCpuUsage(tags?: Record<string, string>): void {
    if (!this.config.enableCpuTracking) return;

    const cpuUsage = process.cpuUsage();
    const timestamp = Date.now();

    this.recordMetric({
      name: 'cpu.user',
      value: cpuUsage.user / 1000, // Convert to milliseconds
      unit: 'ms',
      timestamp,
      tags,
    });

    this.recordMetric({
      name: 'cpu.system',
      value: cpuUsage.system / 1000, // Convert to milliseconds
      unit: 'ms',
      timestamp,
      tags,
    });
  }

  /**
   * Records file operation metrics
   */
  recordFileOperation(operation: string, filePath: string, duration: number, success: boolean): void {
    this.recordMetric({
      name: `file.${operation}.duration`,
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
      tags: {
        operation,
        file_path: filePath,
        success: success.toString(),
      },
    });

    this.recordMetric({
      name: `file.${operation}.count`,
      value: 1,
      unit: 'count',
      timestamp: Date.now(),
      tags: {
        operation,
        success: success.toString(),
      },
    });
  }

  /**
   * Records API call metrics
   */
  recordApiCall(endpoint: string, method: string, duration: number, statusCode: number): void {
    this.recordMetric({
      name: 'api.call.duration',
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
      tags: {
        endpoint,
        method,
        status_code: statusCode.toString(),
        success: (statusCode >= 200 && statusCode < 300).toString(),
      },
    });

    this.recordMetric({
      name: 'api.call.count',
      value: 1,
      unit: 'count',
      timestamp: Date.now(),
      tags: {
        endpoint,
        method,
        status_code: statusCode.toString(),
      },
    });
  }

  /**
   * Records tool execution metrics
   */
  recordToolExecution(toolName: string, duration: number, success: boolean, error?: string): void {
    this.recordMetric({
      name: 'tool.execution.duration',
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
      tags: {
        tool_name: toolName,
        success: success.toString(),
        error_type: error ? 'error' : 'none',
      },
    });

    this.recordMetric({
      name: 'tool.execution.count',
      value: 1,
      unit: 'count',
      timestamp: Date.now(),
      tags: {
        tool_name: toolName,
        success: success.toString(),
      },
    });
  }

  /**
   * Gets performance summary
   */
  getPerformanceSummary(): {
    totalSpans: number;
    totalMetrics: number;
    averageSpanDuration: number;
    memoryUsage?: NodeJS.MemoryUsage;
    topSlowSpans: Array<{ name: string; duration: number }>;
    metricsSummary: Record<string, { count: number; average: number; min: number; max: number }>;
  } {
    const completedSpans = Array.from(this.spans.values()).filter(s => s.duration !== undefined);
    const averageSpanDuration = completedSpans.length > 0 
      ? completedSpans.reduce((sum, span) => sum + (span.duration || 0), 0) / completedSpans.length
      : 0;

    const topSlowSpans = completedSpans
      .sort((a, b) => (b.duration || 0) - (a.duration || 0))
      .slice(0, 10)
      .map(span => ({ name: span.name, duration: span.duration || 0 }));

    // Calculate metrics summary
    const metricsSummary: Record<string, { count: number; average: number; min: number; max: number }> = {};
    
    for (const metric of this.metrics) {
      if (!metricsSummary[metric.name]) {
        metricsSummary[metric.name] = {
          count: 0,
          average: 0,
          min: Number.MAX_VALUE,
          max: Number.MIN_VALUE,
        };
      }

      const summary = metricsSummary[metric.name];
      summary.count++;
      summary.min = Math.min(summary.min, metric.value);
      summary.max = Math.max(summary.max, metric.value);
      summary.average = (summary.average * (summary.count - 1) + metric.value) / summary.count;
    }

    return {
      totalSpans: this.spans.size,
      totalMetrics: this.metrics.length,
      averageSpanDuration,
      memoryUsage: this.config.enableMemoryTracking ? process.memoryUsage() : undefined,
      topSlowSpans,
      metricsSummary,
    };
  }

  /**
   * Gets all spans
   */
  getSpans(): PerformanceSpan[] {
    return Array.from(this.spans.values());
  }

  /**
   * Gets all metrics
   */
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  /**
   * Gets metrics by name pattern
   */
  getMetricsByPattern(pattern: string): PerformanceMetric[] {
    const regex = new RegExp(pattern);
    return this.metrics.filter(metric => regex.test(metric.name));
  }

  /**
   * Clears all performance data
   */
  clear(): void {
    this.spans.clear();
    this.metrics.length = 0;
    this.spanIdCounter = 0;
  }

  /**
   * Exports performance data for external analysis
   */
  exportData(): {
    spans: PerformanceSpan[];
    metrics: PerformanceMetric[];
    summary: ReturnType<PerformanceMonitor['getPerformanceSummary']>;
  } {
    return {
      spans: this.getSpans(),
      metrics: this.getMetrics(),
      summary: this.getPerformanceSummary(),
    };
  }

  /**
   * Creates a performance decorator for functions
   */
  createDecorator<T extends (...args: any[]) => any>(name: string, tags?: Record<string, string>) {
    return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
      const originalMethod = descriptor.value;

      descriptor.value = async function (...args: Parameters<T>): Promise<ReturnType<T>> {
        const spanId = this.startSpan(`${name}.${propertyKey}`, undefined, tags);
        const startTime = performance.now();
        
        try {
          const result = await originalMethod.apply(this, args);
          const duration = performance.now() - startTime;
          
          this.endSpan(spanId, { success: 'true' });
          this.recordMetric({
            name: `method.${name}.${propertyKey}.duration`,
            value: duration,
            unit: 'ms',
            timestamp: Date.now(),
            tags: { ...tags, success: 'true' },
          });
          
          return result;
        } catch (error) {
          const duration = performance.now() - startTime;
          
          this.endSpan(spanId, { success: 'false', error: error instanceof Error ? error.message : 'unknown' });
          this.recordMetric({
            name: `method.${name}.${propertyKey}.duration`,
            value: duration,
            unit: 'ms',
            timestamp: Date.now(),
            tags: { ...tags, success: 'false' },
          });
          
          throw error;
        }
      };

      return descriptor;
    };
  }

  /**
   * Starts automatic memory monitoring
   */
  private startMemoryMonitoring(): void {
    if (!this.config.enableMemoryTracking) return;

    // Record memory usage every 30 seconds
    setInterval(() => {
      this.recordMemoryUsage({ source: 'automatic' });
    }, 30000);
  }

  /**
   * Cleans up old spans to prevent memory leaks
   */
  private cleanupOldSpans(): void {
    if (this.spans.size <= this.config.maxSpans) return;

    const spansArray = Array.from(this.spans.entries());
    spansArray.sort((a, b) => a[1].startTime - b[1].startTime);
    
    const toRemove = spansArray.slice(0, spansArray.length - this.config.maxSpans);
    for (const [spanId] of toRemove) {
      this.spans.delete(spanId);
    }
  }

  /**
   * Cleans up old metrics to prevent memory leaks
   */
  private cleanupOldMetrics(): void {
    if (this.metrics.length <= this.config.maxMetrics) return;

    this.metrics.sort((a, b) => a.timestamp - b.timestamp);
    this.metrics.splice(0, this.metrics.length - this.config.maxMetrics);
  }
}

// Global performance monitor instance
let globalPerformanceMonitor: PerformanceMonitor | null = null;

/**
 * Gets the global performance monitor instance
 */
export function getPerformanceMonitor(): PerformanceMonitor {
  if (!globalPerformanceMonitor) {
    globalPerformanceMonitor = new PerformanceMonitor();
  }
  return globalPerformanceMonitor;
}

/**
 * Initializes the global performance monitor with custom config
 */
export function initializePerformanceMonitor(config: Partial<PerformanceMonitorConfig>): PerformanceMonitor {
  globalPerformanceMonitor = new PerformanceMonitor(config);
  return globalPerformanceMonitor;
}

/**
 * Performance monitoring decorator
 */
export function monitor(name?: string, tags?: Record<string, string>) {
  return getPerformanceMonitor().createDecorator(name || 'unknown', tags);
}

/**
 * Utility function to measure async operations
 */
export async function measureAsync<T>(
  name: string,
  operation: () => Promise<T>,
  tags?: Record<string, string>
): Promise<T> {
  const monitor = getPerformanceMonitor();
  const spanId = monitor.startSpan(name, undefined, tags);
  const startTime = performance.now();
  
  try {
    const result = await operation();
    const duration = performance.now() - startTime;
    
    monitor.endSpan(spanId, { success: 'true' });
    monitor.recordMetric({
      name: `operation.${name}.duration`,
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
      tags: { ...tags, success: 'true' },
    });
    
    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    
    monitor.endSpan(spanId, { success: 'false', error: error instanceof Error ? error.message : 'unknown' });
    monitor.recordMetric({
      name: `operation.${name}.duration`,
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
      tags: { ...tags, success: 'false' },
    });
    
    throw error;
  }
}
