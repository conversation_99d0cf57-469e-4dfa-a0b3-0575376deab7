{"version": "3.2.4", "results": [[":src/utils/editCorrector.test.ts", {"duration": 57.36349999999993, "failed": false}], [":src/tools/edit.test.ts", {"duration": 225.1298999999999, "failed": false}], [":src/tools/write-file.test.ts", {"duration": 244.3691000000008, "failed": false}], [":src/tools/mcp-client.test.ts", {"duration": 59.2958000000001, "failed": false}], [":src/utils/memoryDiscovery.test.ts", {"duration": 50.62259999999992, "failed": true}], [":src/telemetry/loggers.test.ts", {"duration": 249.08320000000003, "failed": false}], [":src/core/logger.test.ts", {"duration": 434.0075999999999, "failed": false}], [":src/tests/integration.test.ts", {"duration": 1247.2660999999998, "failed": true}], [":src/utils/fileUtils.test.ts", {"duration": 236.54380000000015, "failed": false}], [":src/tools/glob.test.ts", {"duration": 2459.7815, "failed": true}], [":src/core/client.test.ts", {"duration": 222.72670000000016, "failed": false}], [":src/tools/read-many-files.test.ts", {"duration": 418.46669999999995, "failed": false}], [":src/utils/retry.test.ts", {"duration": 87.83289999999988, "failed": false}], [":src/core/turn.test.ts", {"duration": 40.67190000000005, "failed": false}], [":src/utils/editor.test.ts", {"duration": 54.444000000000074, "failed": false}], [":src/utils/generateContentResponseUtilities.test.ts", {"duration": 25.338899999999967, "failed": false}], [":src/utils/getFolderStructure.test.ts", {"duration": 179.75649999999996, "failed": true}], [":src/tools/tool-registry.test.ts", {"duration": 209.6185999999998, "failed": false}], [":src/tools/modifiable-tool.test.ts", {"duration": 76.18590000000006, "failed": true}], [":src/tests/astProcessor.test.ts", {"duration": 74.90240000000006, "failed": true}], [":src/tools/memoryTool.test.ts", {"duration": 54.57709999999997, "failed": false}], [":src/tools/mcp-tool.test.ts", {"duration": 21.706999999999994, "failed": false}], [":src/tools/grep.test.ts", {"duration": 1032.3016, "failed": true}], [":src/services/gitService.test.ts", {"duration": 60.65160000000003, "failed": true}], [":src/tests/performanceMonitor.test.ts", {"duration": 125.82600000000002, "failed": true}], [":src/config/config.test.ts", {"duration": 209.21720000000005, "failed": false}], [":src/tools/read-file.test.ts", {"duration": 137.8547000000001, "failed": true}], [":src/core/coreToolScheduler.test.ts", {"duration": 184.66730000000007, "failed": false}], [":src/utils/nextSpeakerChecker.test.ts", {"duration": 95.8077000000003, "failed": false}], [":src/core/nonInteractiveToolExecutor.test.ts", {"duration": 143.17290000000003, "failed": false}], [":src/utils/errorReporting.test.ts", {"duration": 58.84500000000003, "failed": true}], [":src/telemetry/metrics.test.ts", {"duration": 98.86810000000003, "failed": false}], [":src/code_assist/converter.test.ts", {"duration": 13.711500000000115, "failed": false}], [":src/utils/gitIgnoreParser.test.ts", {"duration": 76.22959999999989, "failed": true}], [":src/config/flashFallback.test.ts", {"duration": 79.84439999999995, "failed": false}], [":src/utils/bfsFileSearch.test.ts", {"duration": 51.71259999999995, "failed": true}], [":src/code_assist/server.test.ts", {"duration": 50.218399999999974, "failed": false}], [":src/utils/flashFallback.integration.test.ts", {"duration": 283.42920000000004, "failed": false}], [":src/core/prompts.test.ts", {"duration": 782.9811000000001, "failed": true}], [":src/services/fileDiscoveryService.test.ts", {"duration": 38.69190000000003, "failed": true}], [":src/code_assist/oauth2.test.ts", {"duration": 30.847099999999955, "failed": false}], [":src/tools/web-fetch.test.ts", {"duration": 8.113299999999981, "failed": false}], [":src/core/arienRequest.test.ts", {"duration": 10.666600000000017, "failed": false}], [":src/telemetry/telemetry.test.ts", {"duration": 23.16379999999981, "failed": false}], [":src/core/contentGenerator.test.ts", {"duration": 11.657699999999977, "failed": false}], [":src/index.test.ts", {"duration": 6.851199999999949, "failed": false}], [":src/core/arienChat.test.ts", {"duration": 0, "failed": true}]]}