/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { performance } from 'perf_hooks';
import { getPerformanceMonitor } from '../telemetry/performanceMonitor.js';

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Error categories for classification
 */
export enum ErrorCategory {
  NETWORK = 'network',
  FILE_SYSTEM = 'file_system',
  AUTHENTICATION = 'authentication',
  VALIDATION = 'validation',
  TOOL_EXECUTION = 'tool_execution',
  API = 'api',
  CONFIGURATION = 'configuration',
  MEMORY = 'memory',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown',
}

/**
 * Represents a structured error with additional context
 */
export interface StructuredError {
  id: string;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  timestamp: number;
  stack?: string;
  context?: Record<string, any>;
  userMessage?: string;
  suggestedActions?: string[];
  retryable: boolean;
  originalError?: Error;
}

/**
 * Error recovery strategy
 */
export interface ErrorRecoveryStrategy {
  name: string;
  canRecover: (error: StructuredError) => boolean;
  recover: (error: StructuredError, context?: any) => Promise<any>;
  maxRetries: number;
  backoffMs: number;
}

/**
 * Configuration for error handling
 */
export interface ErrorHandlingConfig {
  enableErrorReporting: boolean;
  enableAutoRecovery: boolean;
  maxErrorHistory: number;
  enableStackTraces: boolean;
  enableUserFriendlyMessages: boolean;
  reportingEndpoint?: string;
}

/**
 * Advanced error handling service
 */
export class AdvancedErrorHandler {
  private config: ErrorHandlingConfig;
  private errorHistory: StructuredError[] = [];
  private recoveryStrategies: ErrorRecoveryStrategy[] = [];
  private errorIdCounter = 0;

  constructor(config: Partial<ErrorHandlingConfig> = {}) {
    this.config = {
      enableErrorReporting: true,
      enableAutoRecovery: true,
      maxErrorHistory: 1000,
      enableStackTraces: true,
      enableUserFriendlyMessages: true,
      ...config,
    };

    this.initializeDefaultRecoveryStrategies();
  }

  /**
   * Handles an error with advanced processing
   */
  async handleError(
    error: Error | StructuredError,
    context?: Record<string, any>
  ): Promise<StructuredError> {
    const structuredError = this.createStructuredError(error, context);
    
    // Record the error
    this.recordError(structuredError);
    
    // Report to telemetry
    this.reportToTelemetry(structuredError);
    
    // Attempt recovery if enabled
    if (this.config.enableAutoRecovery) {
      await this.attemptRecovery(structuredError);
    }
    
    return structuredError;
  }

  /**
   * Creates a structured error from various input types
   */
  createStructuredError(
    error: Error | StructuredError,
    context?: Record<string, any>
  ): StructuredError {
    if (this.isStructuredError(error)) {
      return { ...error, context: { ...error.context, ...context } };
    }

    const category = this.categorizeError(error);
    const severity = this.determineSeverity(error, category);
    const userMessage = this.generateUserFriendlyMessage(error, category);
    const suggestedActions = this.generateSuggestedActions(error, category);

    return {
      id: `error_${++this.errorIdCounter}_${Date.now()}`,
      message: error.message,
      category,
      severity,
      timestamp: Date.now(),
      stack: this.config.enableStackTraces ? error.stack : undefined,
      context,
      userMessage: this.config.enableUserFriendlyMessages ? userMessage : undefined,
      suggestedActions,
      retryable: this.isRetryable(error, category),
      originalError: error,
    };
  }

  /**
   * Registers a custom error recovery strategy
   */
  registerRecoveryStrategy(strategy: ErrorRecoveryStrategy): void {
    this.recoveryStrategies.push(strategy);
  }

  /**
   * Gets error statistics
   */
  getErrorStatistics(): {
    totalErrors: number;
    errorsByCategory: Record<ErrorCategory, number>;
    errorsBySeverity: Record<ErrorSeverity, number>;
    recentErrors: StructuredError[];
    mostCommonErrors: Array<{ message: string; count: number }>;
  } {
    const errorsByCategory = {} as Record<ErrorCategory, number>;
    const errorsBySeverity = {} as Record<ErrorSeverity, number>;
    const errorCounts = new Map<string, number>();

    for (const error of this.errorHistory) {
      errorsByCategory[error.category] = (errorsByCategory[error.category] || 0) + 1;
      errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + 1;
      
      const count = errorCounts.get(error.message) || 0;
      errorCounts.set(error.message, count + 1);
    }

    const mostCommonErrors = Array.from(errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([message, count]) => ({ message, count }));

    const recentErrors = this.errorHistory
      .slice(-10)
      .sort((a, b) => b.timestamp - a.timestamp);

    return {
      totalErrors: this.errorHistory.length,
      errorsByCategory,
      errorsBySeverity,
      recentErrors,
      mostCommonErrors,
    };
  }

  /**
   * Clears error history
   */
  clearErrorHistory(): void {
    this.errorHistory.length = 0;
  }

  /**
   * Gets errors by category
   */
  getErrorsByCategory(category: ErrorCategory): StructuredError[] {
    return this.errorHistory.filter(error => error.category === category);
  }

  /**
   * Gets errors by severity
   */
  getErrorsBySeverity(severity: ErrorSeverity): StructuredError[] {
    return this.errorHistory.filter(error => error.severity === severity);
  }

  /**
   * Creates an error wrapper for async operations
   */
  wrapAsync<T>(
    operation: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    return operation().catch(async (error) => {
      const structuredError = await this.handleError(error, context);
      throw structuredError;
    });
  }

  /**
   * Creates an error wrapper for sync operations
   */
  wrapSync<T>(
    operation: () => T,
    context?: Record<string, any>
  ): T {
    try {
      return operation();
    } catch (error) {
      const structuredError = this.createStructuredError(error as Error, context);
      this.recordError(structuredError);
      this.reportToTelemetry(structuredError);
      throw structuredError;
    }
  }

  /**
   * Records an error in the history
   */
  private recordError(error: StructuredError): void {
    this.errorHistory.push(error);
    
    // Cleanup old errors
    if (this.errorHistory.length > this.config.maxErrorHistory) {
      this.errorHistory.splice(0, this.errorHistory.length - this.config.maxErrorHistory);
    }
  }

  /**
   * Reports error to telemetry system
   */
  private reportToTelemetry(error: StructuredError): void {
    if (!this.config.enableErrorReporting) return;

    try {
      const monitor = getPerformanceMonitor();
      monitor.recordMetric({
        name: 'error.occurred',
        value: 1,
        unit: 'count',
        timestamp: error.timestamp,
        tags: {
          category: error.category,
          severity: error.severity,
          retryable: error.retryable.toString(),
        },
      });
    } catch (telemetryError) {
      console.error('Failed to report error to telemetry:', telemetryError);
    }
  }

  /**
   * Attempts to recover from an error using registered strategies
   */
  private async attemptRecovery(error: StructuredError): Promise<any> {
    for (const strategy of this.recoveryStrategies) {
      if (strategy.canRecover(error)) {
        try {
          console.log(`Attempting recovery with strategy: ${strategy.name}`);
          const result = await strategy.recover(error);
          
          // Record successful recovery
          const monitor = getPerformanceMonitor();
          monitor.recordMetric({
            name: 'error.recovery.success',
            value: 1,
            unit: 'count',
            timestamp: Date.now(),
            tags: {
              strategy: strategy.name,
              error_category: error.category,
            },
          });
          
          return result;
        } catch (recoveryError) {
          console.error(`Recovery strategy ${strategy.name} failed:`, recoveryError);
          
          // Record failed recovery
          const monitor = getPerformanceMonitor();
          monitor.recordMetric({
            name: 'error.recovery.failure',
            value: 1,
            unit: 'count',
            timestamp: Date.now(),
            tags: {
              strategy: strategy.name,
              error_category: error.category,
            },
          });
        }
      }
    }
    
    return null;
  }

  /**
   * Categorizes an error based on its properties
   */
  private categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();
    const name = error.name.toLowerCase();

    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return ErrorCategory.NETWORK;
    }
    
    if (message.includes('enoent') || message.includes('file') || message.includes('directory')) {
      return ErrorCategory.FILE_SYSTEM;
    }
    
    if (message.includes('auth') || message.includes('unauthorized') || message.includes('forbidden')) {
      return ErrorCategory.AUTHENTICATION;
    }
    
    if (message.includes('validation') || message.includes('invalid') || name.includes('validation')) {
      return ErrorCategory.VALIDATION;
    }
    
    if (message.includes('timeout') || name.includes('timeout')) {
      return ErrorCategory.TIMEOUT;
    }
    
    if (message.includes('memory') || message.includes('heap')) {
      return ErrorCategory.MEMORY;
    }
    
    if (message.includes('api') || message.includes('request')) {
      return ErrorCategory.API;
    }
    
    if (message.includes('config') || message.includes('setting')) {
      return ErrorCategory.CONFIGURATION;
    }

    return ErrorCategory.UNKNOWN;
  }

  /**
   * Determines error severity
   */
  private determineSeverity(error: Error, category: ErrorCategory): ErrorSeverity {
    const message = error.message.toLowerCase();

    // Critical errors
    if (message.includes('fatal') || message.includes('critical') || category === ErrorCategory.MEMORY) {
      return ErrorSeverity.CRITICAL;
    }

    // High severity errors
    if (category === ErrorCategory.AUTHENTICATION || category === ErrorCategory.CONFIGURATION) {
      return ErrorSeverity.HIGH;
    }

    // Medium severity errors
    if (category === ErrorCategory.NETWORK || category === ErrorCategory.API) {
      return ErrorSeverity.MEDIUM;
    }

    // Default to low severity
    return ErrorSeverity.LOW;
  }

  /**
   * Generates user-friendly error messages
   */
  private generateUserFriendlyMessage(error: Error, category: ErrorCategory): string {
    switch (category) {
      case ErrorCategory.NETWORK:
        return 'There was a problem connecting to the network. Please check your internet connection and try again.';
      case ErrorCategory.FILE_SYSTEM:
        return 'There was a problem accessing a file or directory. Please check that the path exists and you have the necessary permissions.';
      case ErrorCategory.AUTHENTICATION:
        return 'Authentication failed. Please check your credentials and try again.';
      case ErrorCategory.VALIDATION:
        return 'The provided input is invalid. Please check your input and try again.';
      case ErrorCategory.TIMEOUT:
        return 'The operation timed out. Please try again or check your network connection.';
      case ErrorCategory.MEMORY:
        return 'The application is running low on memory. Please close other applications and try again.';
      case ErrorCategory.API:
        return 'There was a problem with the API request. Please try again later.';
      case ErrorCategory.CONFIGURATION:
        return 'There is a problem with the configuration. Please check your settings.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /**
   * Generates suggested actions for error recovery
   */
  private generateSuggestedActions(error: Error, category: ErrorCategory): string[] {
    const actions: string[] = [];

    switch (category) {
      case ErrorCategory.NETWORK:
        actions.push('Check your internet connection');
        actions.push('Try again in a few moments');
        actions.push('Check if the service is available');
        break;
      case ErrorCategory.FILE_SYSTEM:
        actions.push('Verify the file or directory path');
        actions.push('Check file permissions');
        actions.push('Ensure the file exists');
        break;
      case ErrorCategory.AUTHENTICATION:
        actions.push('Verify your credentials');
        actions.push('Check if your session has expired');
        actions.push('Try logging in again');
        break;
      case ErrorCategory.VALIDATION:
        actions.push('Check the input format');
        actions.push('Verify required fields are provided');
        actions.push('Review the documentation');
        break;
      case ErrorCategory.TIMEOUT:
        actions.push('Try the operation again');
        actions.push('Check your network speed');
        actions.push('Increase timeout settings if possible');
        break;
      case ErrorCategory.MEMORY:
        actions.push('Close unnecessary applications');
        actions.push('Restart the application');
        actions.push('Check available system memory');
        break;
      case ErrorCategory.API:
        actions.push('Try the request again');
        actions.push('Check API status');
        actions.push('Verify API key or credentials');
        break;
      case ErrorCategory.CONFIGURATION:
        actions.push('Review configuration settings');
        actions.push('Check for typos in configuration');
        actions.push('Restore default settings');
        break;
      default:
        actions.push('Try the operation again');
        actions.push('Check the logs for more details');
        actions.push('Contact support if the problem persists');
    }

    return actions;
  }

  /**
   * Determines if an error is retryable
   */
  private isRetryable(error: Error, category: ErrorCategory): boolean {
    const message = error.message.toLowerCase();

    // Non-retryable errors
    if (category === ErrorCategory.AUTHENTICATION || 
        category === ErrorCategory.VALIDATION ||
        message.includes('not found') ||
        message.includes('forbidden')) {
      return false;
    }

    // Retryable errors
    if (category === ErrorCategory.NETWORK ||
        category === ErrorCategory.TIMEOUT ||
        category === ErrorCategory.API ||
        message.includes('temporary') ||
        message.includes('retry')) {
      return true;
    }

    return false;
  }

  /**
   * Checks if an object is a structured error
   */
  private isStructuredError(obj: any): obj is StructuredError {
    return obj && typeof obj === 'object' && 'id' in obj && 'category' in obj && 'severity' in obj;
  }

  /**
   * Initializes default recovery strategies
   */
  private initializeDefaultRecoveryStrategies(): void {
    // Network retry strategy
    this.registerRecoveryStrategy({
      name: 'network-retry',
      canRecover: (error) => error.category === ErrorCategory.NETWORK && error.retryable,
      recover: async (error) => {
        // Implement exponential backoff retry logic
        await new Promise(resolve => setTimeout(resolve, 1000));
        return null;
      },
      maxRetries: 3,
      backoffMs: 1000,
    });

    // File system retry strategy
    this.registerRecoveryStrategy({
      name: 'filesystem-retry',
      canRecover: (error) => error.category === ErrorCategory.FILE_SYSTEM && error.retryable,
      recover: async (error) => {
        // Wait a bit and retry
        await new Promise(resolve => setTimeout(resolve, 500));
        return null;
      },
      maxRetries: 2,
      backoffMs: 500,
    });

    // API retry strategy
    this.registerRecoveryStrategy({
      name: 'api-retry',
      canRecover: (error) => error.category === ErrorCategory.API && error.retryable,
      recover: async (error) => {
        // Implement API-specific retry logic
        await new Promise(resolve => setTimeout(resolve, 2000));
        return null;
      },
      maxRetries: 3,
      backoffMs: 2000,
    });
  }
}

// Global error handler instance
let globalErrorHandler: AdvancedErrorHandler | null = null;

/**
 * Gets the global error handler instance
 */
export function getErrorHandler(): AdvancedErrorHandler {
  if (!globalErrorHandler) {
    globalErrorHandler = new AdvancedErrorHandler();
  }
  return globalErrorHandler;
}

/**
 * Initializes the global error handler with custom config
 */
export function initializeErrorHandler(config: Partial<ErrorHandlingConfig>): AdvancedErrorHandler {
  globalErrorHandler = new AdvancedErrorHandler(config);
  return globalErrorHandler;
}

/**
 * Utility function to handle errors with context
 */
export async function handleError(error: Error, context?: Record<string, any>): Promise<StructuredError> {
  return getErrorHandler().handleError(error, context);
}

/**
 * Utility function to wrap async operations with error handling
 */
export function withErrorHandling<T>(
  operation: () => Promise<T>,
  context?: Record<string, any>
): Promise<T> {
  return getErrorHandler().wrapAsync(operation, context);
}
