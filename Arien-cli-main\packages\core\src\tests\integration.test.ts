/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ASTProcessor } from '../code_assist/astProcessor.js';
import { SymbolAnalysisService } from '../code_assist/symbolAnalysis.js';
import { PerformanceMonitor } from '../telemetry/performanceMonitor.js';
import { AdvancedErrorHandler } from '../utils/advancedErrorHandling.js';
import { AdvancedCache } from '../utils/cacheSystem.js';
import { ToolExecutionEngine } from '../tools/toolExecutionEngine.js';
import { EnhancedFileDiscoveryService } from '../services/enhancedFileDiscovery.js';
import * as fs from 'fs/promises';

// Mock fs module
vi.mock('fs/promises');

describe('Integration Tests', () => {
  let astProcessor: ASTProcessor;
  let symbolAnalysis: SymbolAnalysisService;
  let performanceMonitor: PerformanceMonitor;
  let errorHandler: AdvancedErrorHandler;
  let cache: AdvancedCache;
  let toolEngine: ToolExecutionEngine;
  let fileDiscovery: EnhancedFileDiscoveryService;
  
  const mockFs = vi.mocked(fs);

  beforeEach(() => {
    astProcessor = new ASTProcessor();
    symbolAnalysis = new SymbolAnalysisService();
    performanceMonitor = new PerformanceMonitor({ enableMemoryTracking: false, enableCpuTracking: false });
    errorHandler = new AdvancedErrorHandler();
    cache = new AdvancedCache();
    toolEngine = new ToolExecutionEngine();
    fileDiscovery = new EnhancedFileDiscoveryService('/test/project');
    
    vi.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.clear();
    cache.clear();
    vi.restoreAllMocks();
  });

  describe('Code Analysis Workflow', () => {
    it('should perform complete code analysis with performance monitoring', async () => {
      // Mock file system
      const mockProjectStructure = {
        '/test/project/src/user.ts': `
export interface User {
  id: number;
  name: string;
  email: string;
}

export class UserService {
  private users: User[] = [];

  async getUser(id: number): Promise<User | null> {
    return this.users.find(u => u.id === id) || null;
  }

  async createUser(userData: Omit<User, 'id'>): Promise<User> {
    const user: User = {
      id: Date.now(),
      ...userData,
    };
    this.users.push(user);
    return user;
  }
}
`,
        '/test/project/src/api.ts': `
import { UserService } from './user';

export class ApiController {
  private userService = new UserService();

  async handleGetUser(req: any, res: any) {
    const userId = parseInt(req.params.id);
    const user = await this.userService.getUser(userId);
    
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    
    res.json(user);
  }
}
`,
      };

      // Setup file system mocks
      mockFs.readdir.mockImplementation(async (dirPath: any) => {
        if (dirPath.includes('src')) {
          return [
            { name: 'user.ts', isFile: () => true, isDirectory: () => false },
            { name: 'api.ts', isFile: () => true, isDirectory: () => false },
          ] as any;
        }
        return [
          { name: 'src', isFile: () => false, isDirectory: () => true },
        ] as any;
      });

      mockFs.readFile.mockImplementation(async (filePath: any) => {
        return mockProjectStructure[filePath as keyof typeof mockProjectStructure] || '';
      });

      mockFs.stat.mockImplementation(async (filePath: any) => {
        const isDirectory = filePath.endsWith('src') || filePath.endsWith('project');
        return {
          isDirectory: () => isDirectory,
          isFile: () => !isDirectory,
          size: 1000,
          mtime: new Date(),
        } as any;
      });

      // Start performance monitoring
      const analysisSpanId = performanceMonitor.startSpan('code-analysis-workflow');

      try {
        // 1. Discover files
        const discoverySpanId = performanceMonitor.startSpan('file-discovery', analysisSpanId);
        const discoveryResult = await fileDiscovery.discoverFiles('/test/project', {
          extensions: ['.ts', '.js'],
          maxDepth: 5,
        });
        performanceMonitor.endSpan(discoverySpanId);

        expect(discoveryResult.files.length).toBeGreaterThan(0);
        expect(discoveryResult.files.some(f => f.path.includes('user.ts'))).toBe(true);
        expect(discoveryResult.files.some(f => f.path.includes('api.ts'))).toBe(true);

        // 2. Analyze project structure
        const astSpanId = performanceMonitor.startSpan('ast-analysis', analysisSpanId);
        const projectAnalysis = await symbolAnalysis.analyzeProject('/test/project');
        performanceMonitor.endSpan(astSpanId);

        expect(projectAnalysis.structures.length).toBeGreaterThan(0);
        expect(projectAnalysis.dependencies.length).toBeGreaterThan(0);

        // 3. Find symbol definitions
        const symbolSpanId = performanceMonitor.startSpan('symbol-search', analysisSpanId);
        const userServiceDefinitions = await astProcessor.findSymbolDefinitions(
          'UserService',
          projectAnalysis.structures
        );
        performanceMonitor.endSpan(symbolSpanId);

        expect(userServiceDefinitions.length).toBeGreaterThan(0);
        expect(userServiceDefinitions[0].name).toBe('UserService');
        expect(userServiceDefinitions[0].type).toBe('class');

        // 4. Get completion suggestions
        const completionSpanId = performanceMonitor.startSpan('completion-suggestions', analysisSpanId);
        const suggestions = await symbolAnalysis.getCompletionSuggestions(
          '/test/project/src/api.ts',
          10,
          20
        );
        performanceMonitor.endSpan(completionSpanId);

        expect(suggestions.length).toBeGreaterThan(0);
        expect(suggestions.some(s => s.label === 'UserService')).toBe(true);

        performanceMonitor.endSpan(analysisSpanId, { success: 'true' });

        // Verify performance metrics were recorded
        const summary = performanceMonitor.getPerformanceSummary();
        expect(summary.totalSpans).toBeGreaterThan(4);
        expect(summary.averageSpanDuration).toBeGreaterThan(0);

      } catch (error) {
        performanceMonitor.endSpan(analysisSpanId, { success: 'false' });
        throw error;
      }
    });
  });

  describe('Tool Execution with Error Handling', () => {
    it('should execute tools with comprehensive error handling and caching', async () => {
      // Register a test tool
      toolEngine.registerTool({
        name: 'file-analyzer',
        description: 'Analyzes a file and returns metadata',
        parameters: {
          type: 'object',
          properties: {
            filePath: { type: 'string' },
          },
          required: ['filePath'],
        },
        execute: async (params, context) => {
          const spanId = performanceMonitor.startSpan('file-analysis-tool');
          
          try {
            // Simulate file analysis
            const metadata = await fileDiscovery.getFileMetadata(params.filePath);
            
            if (!metadata) {
              throw new Error(`File not found: ${params.filePath}`);
            }

            performanceMonitor.endSpan(spanId, { success: 'true' });
            return {
              path: metadata.path,
              size: metadata.size,
              language: metadata.language,
              lineCount: metadata.lineCount,
            };
          } catch (error) {
            performanceMonitor.endSpan(spanId, { success: 'false' });
            throw error;
          }
        },
        validate: (params) => {
          if (!params.filePath || typeof params.filePath !== 'string') {
            return 'filePath must be a non-empty string';
          }
          return true;
        },
        cacheable: true,
        cacheTtl: 60000, // 1 minute
      });

      // Mock file metadata
      mockFs.stat.mockResolvedValue({
        isDirectory: () => false,
        isFile: () => true,
        size: 1500,
        mtime: new Date(),
      } as any);

      mockFs.readFile.mockResolvedValue('export class TestClass {}');

      const context = {
        sessionId: 'test-session',
        workspaceRoot: '/test/project',
        environment: {},
        permissions: {
          allowFileSystem: true,
          allowNetwork: false,
          allowShell: false,
        },
        timeout: 5000,
        retryPolicy: {
          maxRetries: 2,
          backoffMs: 100,
          retryableErrors: ['ENOENT', 'timeout'],
        },
      };

      // First execution (should hit the actual tool)
      const result1 = await toolEngine.executeTool(
        'file-analyzer',
        { filePath: '/test/project/src/test.ts' },
        context
      );

      expect(result1.success).toBe(true);
      expect(result1.fromCache).toBe(false);
      expect(result1.result).toBeDefined();
      expect(result1.result.language).toBe('typescript');

      // Second execution (should hit cache)
      const result2 = await toolEngine.executeTool(
        'file-analyzer',
        { filePath: '/test/project/src/test.ts' },
        context
      );

      expect(result2.success).toBe(true);
      expect(result2.fromCache).toBe(true);
      expect(result2.result).toEqual(result1.result);

      // Verify statistics
      const stats = toolEngine.getExecutionStats();
      expect(stats.totalExecutions).toBe(2);
      expect(stats.successfulExecutions).toBe(2);
      expect(stats.cacheHitRate).toBe(0.5); // 50% cache hit rate
    });

    it('should handle tool execution errors with retry logic', async () => {
      let attemptCount = 0;

      // Register a flaky tool that fails twice then succeeds
      toolEngine.registerTool({
        name: 'flaky-tool',
        description: 'A tool that fails sometimes',
        parameters: {},
        execute: async () => {
          attemptCount++;
          if (attemptCount <= 2) {
            throw new Error('Temporary failure');
          }
          return { success: true, attempt: attemptCount };
        },
      });

      const context = {
        sessionId: 'test-session',
        workspaceRoot: '/test/project',
        environment: {},
        permissions: {
          allowFileSystem: true,
          allowNetwork: false,
          allowShell: false,
        },
        timeout: 5000,
        retryPolicy: {
          maxRetries: 3,
          backoffMs: 50,
          retryableErrors: ['Temporary failure'],
        },
      };

      const result = await toolEngine.executeTool('flaky-tool', {}, context);

      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(2);
      expect(result.result.attempt).toBe(3);

      // Verify error handling statistics
      const errorStats = errorHandler.getErrorStatistics();
      expect(errorStats.totalErrors).toBeGreaterThan(0);
    });
  });

  describe('Caching Integration', () => {
    it('should demonstrate advanced caching with tags and eviction', async () => {
      // Test different cache operations
      await cache.set('user:123', { id: 123, name: 'John' }, 60000, ['user', 'active']);
      await cache.set('user:456', { id: 456, name: 'Jane' }, 60000, ['user', 'inactive']);
      await cache.set('config:app', { theme: 'dark' }, 60000, ['config']);

      // Test retrieval
      const user123 = await cache.get('user:123');
      expect(user123).toEqual({ id: 123, name: 'John' });

      // Test tag-based operations
      const userEntries = cache.getByTag('user');
      expect(userEntries).toHaveLength(2);

      const activeUsers = cache.getByTag('active');
      expect(activeUsers).toHaveLength(1);
      expect(activeUsers[0].value.name).toBe('John');

      // Test cache statistics
      const stats = cache.getStats();
      expect(stats.totalEntries).toBe(3);
      expect(stats.hitCount).toBe(1); // From the get operation
      expect(stats.hitRate).toBeGreaterThan(0);

      // Test tag-based deletion
      const deletedCount = await cache.deleteByTag('user');
      expect(deletedCount).toBe(2);

      const remainingEntries = cache.keys();
      expect(remainingEntries).toHaveLength(1);
      expect(remainingEntries[0]).toBe('config:app');
    });
  });

  describe('Error Handling Integration', () => {
    it('should demonstrate comprehensive error handling across components', async () => {
      // Test error categorization and recovery
      const networkError = new Error('fetch failed: network timeout');
      const fileError = new Error('ENOENT: file not found');
      const validationError = new Error('Invalid input: missing required field');

      const networkStructuredError = await errorHandler.handleError(networkError, {
        operation: 'api-call',
        endpoint: '/api/users',
      });

      const fileStructuredError = await errorHandler.handleError(fileError, {
        operation: 'file-read',
        path: '/missing/file.txt',
      });

      const validationStructuredError = await errorHandler.handleError(validationError, {
        operation: 'input-validation',
        field: 'email',
      });

      // Verify error categorization
      expect(networkStructuredError.category).toBe('network');
      expect(networkStructuredError.retryable).toBe(true);
      expect(networkStructuredError.suggestedActions).toContain('Check your internet connection');

      expect(fileStructuredError.category).toBe('file_system');
      expect(fileStructuredError.suggestedActions).toContain('Verify the file or directory path');

      expect(validationStructuredError.category).toBe('validation');
      expect(validationStructuredError.retryable).toBe(false);
      expect(validationStructuredError.suggestedActions).toContain('Check the input format');

      // Verify error statistics
      const stats = errorHandler.getErrorStatistics();
      expect(stats.totalErrors).toBe(3);
      expect(stats.errorsByCategory.network).toBe(1);
      expect(stats.errorsByCategory.file_system).toBe(1);
      expect(stats.errorsByCategory.validation).toBe(1);
    });
  });

  describe('Performance Monitoring Integration', () => {
    it('should track performance across all components', async () => {
      // Simulate a complex workflow with multiple components
      const workflowSpanId = performanceMonitor.startSpan('complex-workflow');

      try {
        // File discovery
        const discoverySpanId = performanceMonitor.startSpan('file-discovery', workflowSpanId);
        mockFs.readdir.mockResolvedValue([
          { name: 'test.ts', isFile: () => true, isDirectory: () => false },
        ] as any);
        mockFs.stat.mockResolvedValue({
          isDirectory: () => false,
          isFile: () => true,
          size: 1000,
          mtime: new Date(),
        } as any);
        
        await fileDiscovery.discoverFiles('/test', { extensions: ['.ts'] });
        performanceMonitor.endSpan(discoverySpanId);

        // Cache operations
        const cacheSpanId = performanceMonitor.startSpan('cache-operations', workflowSpanId);
        await cache.set('test-key', { data: 'test' });
        await cache.get('test-key');
        performanceMonitor.endSpan(cacheSpanId);

        // Tool execution
        toolEngine.registerTool({
          name: 'test-tool',
          description: 'Test tool',
          parameters: {},
          execute: async () => ({ result: 'success' }),
        });

        const toolSpanId = performanceMonitor.startSpan('tool-execution', workflowSpanId);
        await toolEngine.executeTool('test-tool', {}, {
          sessionId: 'test',
          workspaceRoot: '/test',
          environment: {},
          permissions: { allowFileSystem: true, allowNetwork: false, allowShell: false },
          timeout: 5000,
        });
        performanceMonitor.endSpan(toolSpanId);

        performanceMonitor.endSpan(workflowSpanId, { success: 'true' });

        // Verify comprehensive performance tracking
        const summary = performanceMonitor.getPerformanceSummary();
        expect(summary.totalSpans).toBeGreaterThan(3);
        expect(summary.topSlowSpans.length).toBeGreaterThan(0);
        
        const exportedData = performanceMonitor.exportData();
        expect(exportedData.spans.length).toBeGreaterThan(3);
        expect(exportedData.metrics.length).toBeGreaterThan(0);

      } catch (error) {
        performanceMonitor.endSpan(workflowSpanId, { success: 'false' });
        throw error;
      }
    });
  });
});
