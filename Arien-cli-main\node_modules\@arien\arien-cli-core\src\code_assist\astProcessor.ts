/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * Represents a symbol found in code analysis
 */
export interface CodeSymbol {
  name: string;
  type: 'function' | 'class' | 'interface' | 'variable' | 'constant' | 'method' | 'property';
  location: {
    file: string;
    line: number;
    column: number;
  };
  scope: string;
  signature?: string;
  documentation?: string;
  visibility?: 'public' | 'private' | 'protected';
}

/**
 * Represents the structure of a code file
 */
export interface FileStructure {
  file: string;
  language: string;
  symbols: CodeSymbol[];
  imports: string[];
  exports: string[];
  dependencies: string[];
}

/**
 * Configuration for AST processing
 */
export interface ASTProcessorConfig {
  includePrivate?: boolean;
  includeDocumentation?: boolean;
  maxDepth?: number;
  excludePatterns?: string[];
}

/**
 * Abstract Syntax Tree processor for code analysis
 */
export class ASTProcessor {
  private config: ASTProcessorConfig;

  constructor(config: ASTProcessorConfig = {}) {
    this.config = {
      includePrivate: false,
      includeDocumentation: true,
      maxDepth: 10,
      excludePatterns: ['node_modules/**', '.git/**', 'dist/**', 'build/**'],
      ...config,
    };
  }

  /**
   * Analyzes a single file and extracts its structure
   */
  async analyzeFile(filePath: string): Promise<FileStructure | null> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const language = this.detectLanguage(filePath);
      
      if (!this.isSupportedLanguage(language)) {
        return null;
      }

      const symbols = await this.extractSymbols(content, filePath, language);
      const imports = this.extractImports(content, language);
      const exports = this.extractExports(content, language);
      const dependencies = this.extractDependencies(content, language);

      return {
        file: filePath,
        language,
        symbols,
        imports,
        exports,
        dependencies,
      };
    } catch (error) {
      console.error(`Error analyzing file ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Analyzes multiple files in a directory
   */
  async analyzeDirectory(dirPath: string): Promise<FileStructure[]> {
    const results: FileStructure[] = [];
    
    try {
      const files = await this.getCodeFiles(dirPath);
      
      for (const file of files) {
        const structure = await this.analyzeFile(file);
        if (structure) {
          results.push(structure);
        }
      }
    } catch (error) {
      console.error(`Error analyzing directory ${dirPath}:`, error);
    }

    return results;
  }

  /**
   * Finds symbol definitions across multiple files
   */
  async findSymbolDefinitions(symbolName: string, files: FileStructure[]): Promise<CodeSymbol[]> {
    const definitions: CodeSymbol[] = [];

    for (const file of files) {
      for (const symbol of file.symbols) {
        if (symbol.name === symbolName || symbol.name.includes(symbolName)) {
          definitions.push(symbol);
        }
      }
    }

    return definitions;
  }

  /**
   * Finds symbol references across multiple files
   */
  async findSymbolReferences(symbolName: string, files: string[]): Promise<Array<{ file: string; line: number; context: string }>> {
    const references: Array<{ file: string; line: number; context: string }> = [];

    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf-8');
        const lines = content.split('\n');

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          if (line.includes(symbolName)) {
            references.push({
              file,
              line: i + 1,
              context: line.trim(),
            });
          }
        }
      } catch (error) {
        console.error(`Error reading file ${file}:`, error);
      }
    }

    return references;
  }

  /**
   * Extracts symbols from code content
   */
  private async extractSymbols(content: string, filePath: string, language: string): Promise<CodeSymbol[]> {
    const symbols: CodeSymbol[] = [];
    const lines = content.split('\n');

    switch (language) {
      case 'typescript':
      case 'javascript':
        return this.extractJSSymbols(lines, filePath);
      case 'python':
        return this.extractPythonSymbols(lines, filePath);
      case 'java':
        return this.extractJavaSymbols(lines, filePath);
      case 'csharp':
        return this.extractCSharpSymbols(lines, filePath);
      default:
        return this.extractGenericSymbols(lines, filePath);
    }
  }

  /**
   * Extracts JavaScript/TypeScript symbols
   */
  private extractJSSymbols(lines: string[], filePath: string): CodeSymbol[] {
    const symbols: CodeSymbol[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Function declarations
      const funcMatch = line.match(/^(?:export\s+)?(?:async\s+)?function\s+(\w+)/);
      if (funcMatch) {
        symbols.push({
          name: funcMatch[1],
          type: 'function',
          location: { file: filePath, line: i + 1, column: 0 },
          scope: 'global',
          signature: line,
        });
      }

      // Class declarations
      const classMatch = line.match(/^(?:export\s+)?class\s+(\w+)/);
      if (classMatch) {
        symbols.push({
          name: classMatch[1],
          type: 'class',
          location: { file: filePath, line: i + 1, column: 0 },
          scope: 'global',
          signature: line,
        });
      }

      // Interface declarations
      const interfaceMatch = line.match(/^(?:export\s+)?interface\s+(\w+)/);
      if (interfaceMatch) {
        symbols.push({
          name: interfaceMatch[1],
          type: 'interface',
          location: { file: filePath, line: i + 1, column: 0 },
          scope: 'global',
          signature: line,
        });
      }

      // Variable declarations
      const varMatch = line.match(/^(?:export\s+)?(?:const|let|var)\s+(\w+)/);
      if (varMatch) {
        symbols.push({
          name: varMatch[1],
          type: line.includes('const') ? 'constant' : 'variable',
          location: { file: filePath, line: i + 1, column: 0 },
          scope: 'global',
          signature: line,
        });
      }

      // Method declarations (inside classes)
      const methodMatch = line.match(/^\s+(?:public|private|protected)?\s*(?:async\s+)?(\w+)\s*\(/);
      if (methodMatch) {
        symbols.push({
          name: methodMatch[1],
          type: 'method',
          location: { file: filePath, line: i + 1, column: 0 },
          scope: 'class',
          signature: line,
          visibility: this.extractVisibility(line),
        });
      }
    }

    return symbols;
  }

  /**
   * Extracts Python symbols
   */
  private extractPythonSymbols(lines: string[], filePath: string): CodeSymbol[] {
    const symbols: CodeSymbol[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Function definitions
      const funcMatch = line.match(/^def\s+(\w+)/);
      if (funcMatch) {
        symbols.push({
          name: funcMatch[1],
          type: 'function',
          location: { file: filePath, line: i + 1, column: 0 },
          scope: 'global',
          signature: line,
        });
      }

      // Class definitions
      const classMatch = line.match(/^class\s+(\w+)/);
      if (classMatch) {
        symbols.push({
          name: classMatch[1],
          type: 'class',
          location: { file: filePath, line: i + 1, column: 0 },
          scope: 'global',
          signature: line,
        });
      }
    }

    return symbols;
  }

  /**
   * Extracts Java symbols
   */
  private extractJavaSymbols(lines: string[], filePath: string): CodeSymbol[] {
    const symbols: CodeSymbol[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Class declarations
      const classMatch = line.match(/^(?:public|private|protected)?\s*class\s+(\w+)/);
      if (classMatch) {
        symbols.push({
          name: classMatch[1],
          type: 'class',
          location: { file: filePath, line: i + 1, column: 0 },
          scope: 'global',
          signature: line,
          visibility: this.extractVisibility(line),
        });
      }

      // Method declarations
      const methodMatch = line.match(/^(?:public|private|protected)?\s*(?:static\s+)?(?:\w+\s+)?(\w+)\s*\(/);
      if (methodMatch && !line.includes('class')) {
        symbols.push({
          name: methodMatch[1],
          type: 'method',
          location: { file: filePath, line: i + 1, column: 0 },
          scope: 'class',
          signature: line,
          visibility: this.extractVisibility(line),
        });
      }
    }

    return symbols;
  }

  /**
   * Extracts C# symbols
   */
  private extractCSharpSymbols(lines: string[], filePath: string): CodeSymbol[] {
    // Similar to Java but with C# specific syntax
    return this.extractJavaSymbols(lines, filePath);
  }

  /**
   * Extracts generic symbols for unsupported languages
   */
  private extractGenericSymbols(lines: string[], filePath: string): CodeSymbol[] {
    const symbols: CodeSymbol[] = [];
    
    // Basic pattern matching for common constructs
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Look for function-like patterns
      if (line.includes('function') || line.includes('def ') || line.includes('func ')) {
        const match = line.match(/(\w+)\s*\(/);
        if (match) {
          symbols.push({
            name: match[1],
            type: 'function',
            location: { file: filePath, line: i + 1, column: 0 },
            scope: 'global',
            signature: line,
          });
        }
      }
    }

    return symbols;
  }

  /**
   * Extracts import statements
   */
  private extractImports(content: string, language: string): string[] {
    const imports: string[] = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      
      switch (language) {
        case 'typescript':
        case 'javascript':
          if (trimmed.startsWith('import ') || trimmed.startsWith('const ') && trimmed.includes('require(')) {
            imports.push(trimmed);
          }
          break;
        case 'python':
          if (trimmed.startsWith('import ') || trimmed.startsWith('from ')) {
            imports.push(trimmed);
          }
          break;
        case 'java':
          if (trimmed.startsWith('import ')) {
            imports.push(trimmed);
          }
          break;
      }
    }

    return imports;
  }

  /**
   * Extracts export statements
   */
  private extractExports(content: string, language: string): string[] {
    const exports: string[] = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      
      if (language === 'typescript' || language === 'javascript') {
        if (trimmed.startsWith('export ') || trimmed.includes('module.exports')) {
          exports.push(trimmed);
        }
      }
    }

    return exports;
  }

  /**
   * Extracts dependencies from content
   */
  private extractDependencies(content: string, language: string): string[] {
    const dependencies: string[] = [];
    const imports = this.extractImports(content, language);

    for (const importLine of imports) {
      const match = importLine.match(/from\s+['"]([^'"]+)['"]|require\(['"]([^'"]+)['"]\)|import\s+['"]([^'"]+)['"]/);
      if (match) {
        const dep = match[1] || match[2] || match[3];
        if (dep && !dep.startsWith('.')) {
          dependencies.push(dep);
        }
      }
    }

    return [...new Set(dependencies)]; // Remove duplicates
  }

  /**
   * Detects the programming language from file extension
   */
  private detectLanguage(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    
    switch (ext) {
      case '.ts':
      case '.tsx':
        return 'typescript';
      case '.js':
      case '.jsx':
        return 'javascript';
      case '.py':
        return 'python';
      case '.java':
        return 'java';
      case '.cs':
        return 'csharp';
      case '.cpp':
      case '.cc':
      case '.cxx':
        return 'cpp';
      case '.c':
        return 'c';
      case '.go':
        return 'go';
      case '.rs':
        return 'rust';
      case '.php':
        return 'php';
      case '.rb':
        return 'ruby';
      default:
        return 'unknown';
    }
  }

  /**
   * Checks if the language is supported for analysis
   */
  private isSupportedLanguage(language: string): boolean {
    return ['typescript', 'javascript', 'python', 'java', 'csharp'].includes(language);
  }

  /**
   * Extracts visibility modifier from a line of code
   */
  private extractVisibility(line: string): 'public' | 'private' | 'protected' | undefined {
    if (line.includes('private')) return 'private';
    if (line.includes('protected')) return 'protected';
    if (line.includes('public')) return 'public';
    return undefined;
  }

  /**
   * Gets all code files in a directory recursively
   */
  private async getCodeFiles(dirPath: string): Promise<string[]> {
    const files: string[] = [];
    const codeExtensions = ['.ts', '.tsx', '.js', '.jsx', '.py', '.java', '.cs', '.cpp', '.c', '.go', '.rs', '.php', '.rb'];

    const traverse = async (currentPath: string, depth: number = 0): Promise<void> => {
      if (depth > (this.config.maxDepth || 10)) return;

      try {
        const entries = await fs.readdir(currentPath, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(currentPath, entry.name);

          if (entry.isDirectory()) {
            // Skip excluded directories
            if (!this.shouldExclude(fullPath)) {
              await traverse(fullPath, depth + 1);
            }
          } else if (entry.isFile()) {
            const ext = path.extname(entry.name).toLowerCase();
            if (codeExtensions.includes(ext) && !this.shouldExclude(fullPath)) {
              files.push(fullPath);
            }
          }
        }
      } catch (error) {
        console.error(`Error reading directory ${currentPath}:`, error);
      }
    };

    await traverse(dirPath);
    return files;
  }

  /**
   * Checks if a path should be excluded based on patterns
   */
  private shouldExclude(filePath: string): boolean {
    const patterns = this.config.excludePatterns || [];
    
    for (const pattern of patterns) {
      // Simple glob-like matching
      const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
      if (regex.test(filePath)) {
        return true;
      }
    }

    return false;
  }
}
