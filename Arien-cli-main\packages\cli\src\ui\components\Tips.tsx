/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { type Config } from '@arien/arien-cli-core';

interface TipsProps {
  config: Config;
}

export const Tips: React.FC<TipsProps> = ({ config }) => {
  const arienMdFileCount = config.getArienMdFileCount();
  
  return (
    <Box flexDirection="column" marginBottom={1} paddingLeft={1}>
      <Box marginBottom={0}>
        <Text color={Colors.AccentCyan} bold>Quick Start Tips</Text>
      </Box>
      
      <Box flexDirection="column" paddingLeft={2}>
        <Text color={Colors.Foreground}>
          <Text color={Colors.Gray}>•</Text> Ask questions, edit files, or run commands
        </Text>
        <Text color={Colors.Foreground}>
          <Text color={Colors.Gray}>•</Text> Be specific for the best results
        </Text>
        <Text color={Colors.Foreground}>
          <Text color={Colors.Gray}>•</Text> Built-in tools available: file operations, web search, browser automation, and more
        </Text>
        {arienMdFileCount === 0 && (
          <Text color={Colors.Foreground}>
            <Text color={Colors.Gray}>•</Text> Create{' '}
            <Text bold color={Colors.AccentPurple}>
              ARIEN.md
            </Text>{' '}
            files to customize your interactions
          </Text>
        )}
        <Text color={Colors.Foreground}>
          <Text color={Colors.Gray}>•</Text> Type{' '}
          <Text bold color={Colors.AccentPurple}>
            /help
          </Text>{' '}
          for commands and shortcuts
        </Text>
      </Box>
    </Box>
  );
};
